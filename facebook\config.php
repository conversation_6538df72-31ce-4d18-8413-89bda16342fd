<?php
/**
 * Facebook Graph Search Configuration
 */

// Database configuration
define('DB_HOST', 'localhost');
define('DB_NAME', 'facebook_search');
define('DB_USER', 'root');
define('DB_PASS', '');

// Facebook API configuration
define('FACEBOOK_APP_ID', '');
define('FACEBOOK_APP_SECRET', '');
define('FACEBOOK_REDIRECT_URI', '');

// Application settings
define('APP_NAME', 'Facebook Graph Search');
define('APP_VERSION', '1.0.0');
define('DEBUG_MODE', true);

// Security settings
define('SESSION_TIMEOUT', 3600); // 1 hour
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOCKOUT_DURATION', 900); // 15 minutes

// License settings
define('LICENSE_API_URL', 'https://api.eliteappsuite.com/license/');
define('LICENSE_VALIDATION_ENDPOINT', 'validate');
define('DEVICE_REGISTRATION_ENDPOINT', 'device/register');

// Error reporting
if (DEBUG_MODE) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

// Start session
session_start();

// Set timezone
date_default_timezone_set('UTC');
?>