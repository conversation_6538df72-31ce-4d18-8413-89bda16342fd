-- Facebook Graph Search Database Schema
-- Created: 2024-08-09
-- Version: 1.0

-- Create database
CREATE DATABASE IF NOT EXISTS facebook_search;
USE facebook_search;

-- Users table
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VA<PERSON>HA<PERSON>(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VA<PERSON><PERSON><PERSON>(50),
    last_name VA<PERSON><PERSON><PERSON>(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active'
);

-- Licenses table
CREATE TABLE IF NOT EXISTS licenses (
    id INT AUTO_INCREMENT PRIMARY KEY,
    license_key VARCHAR(100) UNIQUE NOT NULL,
    user_id INT,
    tier ENUM('trial', 'basic', 'professional', 'enterprise') NOT NULL,
    device_limit INT DEFAULT 1,
    devices_used INT DEFAULT 0,
    expiration_date DATETIME NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    status ENUM('active', 'expired', 'revoked') DEFAULT 'active',
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);

-- Device registrations table
CREATE TABLE IF NOT EXISTS device_registrations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    license_id INT NOT NULL,
    device_fingerprint VARCHAR(255) NOT NULL,
    device_info JSON,
    first_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    status ENUM('active', 'inactive') DEFAULT 'active',
    FOREIGN KEY (license_id) REFERENCES licenses(id) ON DELETE CASCADE,
    UNIQUE KEY unique_license_device (license_id, device_fingerprint)
);

-- Search logs table
CREATE TABLE IF NOT EXISTS search_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    license_id INT,
    search_query TEXT NOT NULL,
    search_type ENUM('basic', 'bulk', 'visual') DEFAULT 'basic',
    results_count INT DEFAULT 0,
    search_engine VARCHAR(20) DEFAULT 'google',
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (license_id) REFERENCES licenses(id) ON DELETE SET NULL
);

-- Profile clicks table
CREATE TABLE IF NOT EXISTS profile_clicks (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    search_log_id INT,
    profile_url TEXT NOT NULL,
    profile_name VARCHAR(255),
    profile_title VARCHAR(255),
    profile_company VARCHAR(255),
    clicked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (search_log_id) REFERENCES search_logs(id) ON DELETE SET NULL
);

-- Admin users table
CREATE TABLE IF NOT EXISTS admin_users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role ENUM('admin', 'moderator') DEFAULT 'admin',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL,
    status ENUM('active', 'inactive') DEFAULT 'active'
);

-- Insert default admin user
INSERT INTO admin_users (username, email, password_hash, role) VALUES 
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin')
ON DUPLICATE KEY UPDATE username = username;

-- Create indexes for better performance
CREATE INDEX idx_licenses_user_id ON licenses(user_id);
CREATE INDEX idx_licenses_status ON licenses(status);
CREATE INDEX idx_licenses_expiration ON licenses(expiration_date);
CREATE INDEX idx_device_registrations_license ON device_registrations(license_id);
CREATE INDEX idx_device_registrations_fingerprint ON device_registrations(device_fingerprint);
CREATE INDEX idx_search_logs_user_id ON search_logs(user_id);
CREATE INDEX idx_search_logs_created_at ON search_logs(created_at);
CREATE INDEX idx_profile_clicks_user_id ON profile_clicks(user_id);
CREATE INDEX idx_profile_clicks_search_log ON profile_clicks(search_log_id);

-- Create views for common queries
CREATE VIEW active_licenses AS
SELECT 
    l.*,
    u.username,
    u.email,
    COUNT(dr.id) as devices_registered
FROM licenses l
LEFT JOIN users u ON l.user_id = u.id
LEFT JOIN device_registrations dr ON l.id = dr.license_id AND dr.status = 'active'
WHERE l.status = 'active' AND l.expiration_date > NOW()
GROUP BY l.id;

CREATE VIEW license_usage_stats AS
SELECT 
    DATE(sl.created_at) as search_date,
    l.tier,
    COUNT(sl.id) as total_searches,
    COUNT(DISTINCT sl.user_id) as unique_users
FROM search_logs sl
JOIN licenses l ON sl.license_id = l.id
WHERE sl.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY DATE(sl.created_at), l.tier
ORDER BY search_date DESC;
