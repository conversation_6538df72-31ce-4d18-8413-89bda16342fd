<?php
/**
 * Facebook License Simulator API
 */

require_once 'config.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// License simulation database
$LICENSE_DATABASE = [
    'FB-TRIAL-2024' => [
        'key' => 'FB-TRIAL-2024',
        'tier' => 'trial',
        'deviceLimit' => 1,
        'expirationDate' => date('Y-m-d H:i:s', strtotime('+7 days')),
        'allowedDevices' => [],
        'features' => [
            'basicSearch' => true,
            'bulkSearch' => false,
            'analytics' => false,
            'export' => false
        ]
    ],
    'FB-PRO-2024' => [
        'key' => 'FB-PRO-2024',
        'tier' => 'professional',
        'deviceLimit' => 5,
        'expirationDate' => date('Y-m-d H:i:s', strtotime('+365 days')),
        'allowedDevices' => [],
        'features' => [
            'basicSearch' => true,
            'bulkSearch' => true,
            'analytics' => true,
            'export' => true
        ]
    ]
];

// API endpoints
$action = $_GET['action'] ?? $_POST['action'] ?? '';

switch ($action) {
    case 'validate':
        validateLicense();
        break;
    case 'simulate':
        simulateLicense();
        break;
    default:
        echo json_encode(['error' => 'Invalid action']);
        break;
}

function validateLicense() {
    global $LICENSE_DATABASE;

    $licenseKey = $_POST['license_key'] ?? '';
    $deviceFingerprint = $_POST['device_fingerprint'] ?? '';

    if (!isset($LICENSE_DATABASE[$licenseKey])) {
        echo json_encode([
            'valid' => false,
            'reason' => 'Invalid license key'
        ]);
        return;
    }

    $license = $LICENSE_DATABASE[$licenseKey];

    // Check expiration
    if (strtotime($license['expirationDate']) < time()) {
        echo json_encode([
            'valid' => false,
            'reason' => 'License has expired'
        ]);
        return;
    }

    // Check device limit
    if (!in_array($deviceFingerprint, $license['allowedDevices'])) {
        if (count($license['allowedDevices']) >= $license['deviceLimit']) {
            echo json_encode([
                'valid' => false,
                'reason' => 'Device limit exceeded'
            ]);
            return;
        }
    }

    echo json_encode([
        'valid' => true,
        'tier' => $license['tier'],
        'features' => $license['features'],
        'expirationDate' => $license['expirationDate']
    ]);
}

function simulateLicense() {
    // Simulation logic for testing
    echo json_encode([
        'status' => 'simulation_complete',
        'message' => 'License simulation completed'
    ]);
}
?>