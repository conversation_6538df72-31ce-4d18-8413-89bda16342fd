
<!DOCTYPE html>
<html lang="en" class="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Facebook Graph Search</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>

    <!-- Export Libraries -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>

    <script>
        // Dynamic Configuration for JavaScript
        window.APP_CONFIG = {
            baseUrl: 'http://localhost/linked/facebook',
            rootUrl: 'http://localhost',
            adminUrl: 'http://localhost/linked/facebook/admin',
            supportUrl: 'https://wa.me/270618757667?text=Hello!%20I%20need%20assistance%20with%20Facebook%20Graph%20Search.'
        };

        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        facebook: {
                            blue: '#1877f2',
                            darkBlue: '#166fe5'
                        }
                    }
                }
            }
        }
    </script>

    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        }

        .facebook-gradient {
            background: linear-gradient(135deg, #1877f2 0%, #166fe5 50%, #0d5bd9 100%);
            background-size: 200% 200%;
            animation: gradientShift 8s ease infinite;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .glass-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .modern-input {
            transition: all 0.3s ease;
            border: 2px solid #e5e7eb;
        }

        .modern-input:focus {
            border-color: #1877f2;
            box-shadow: 0 0 0 4px rgba(24, 119, 242, 0.1);
            outline: none;
        }

        .btn-facebook {
            background: linear-gradient(135deg, #1877f2, #166fe5);
            transition: all 0.3s ease;
        }

        .btn-facebook:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(24, 119, 242, 0.3);
        }

        .search-suggestion {
            transition: all 0.2s ease;
            cursor: pointer;
        }

        .search-suggestion:hover {
            background: #f3f4f6;
            transform: translateX(4px);
        }

        .trending-tag {
            background: linear-gradient(135deg, #fef3c7, #fde68a);
            border: 1px solid #f59e0b;
            transition: all 0.2s ease;
        }

        .trending-tag:hover {
            background: linear-gradient(135deg, #f59e0b, #d97706);
            color: white;
            transform: scale(1.05);
        }

        .profile-enhancement-card {
            background: linear-gradient(135deg, #ede9fe, #ddd6fe);
            border: 1px solid #8b5cf6;
        }

        .mutual-friends-card {
            background: linear-gradient(135deg, #dbeafe, #bfdbfe);
            border: 1px solid #3b82f6;
        }

        .shared-interests-card {
            background: linear-gradient(135deg, #fce7f3, #fbcfe8);
            border: 1px solid #ec4899;
        }

        .tab-button {
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .tab-button.active {
            background: #1877f2;
            color: white;
            box-shadow: 0 4px 12px rgba(24, 119, 242, 0.3);
        }

        .tab-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s ease;
        }

        .tab-button:hover::before {
            left: 100%;
        }

        .search-history-item {
            transition: all 0.2s ease;
            border-left: 3px solid transparent;
        }

        .search-history-item:hover {
            background: #f8fafc;
            border-left-color: #1877f2;
            transform: translateX(4px);
        }

        .keyboard-shortcut {
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid rgba(24, 119, 242, 0.2);
            border-radius: 6px;
            padding: 4px 8px;
            font-size: 11px;
            color: #1877f2;
            backdrop-filter: blur(4px);
        }

        .feature-card {
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .feature-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
        }

        .loading-spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #1877f2;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .pulse-animation {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
    </style>
</head>
<body class="min-h-screen">
    <!-- Main Container -->
    <div class="container mx-auto px-4 py-6 max-w-6xl">

        <!-- Header Section -->
        <div class="facebook-gradient rounded-2xl shadow-2xl mb-8 overflow-hidden">
            <div class="px-8 py-10">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="bg-white rounded-full p-4 mr-6 shadow-lg">
                            <i class="fab fa-facebook text-3xl text-facebook-blue"></i>
                        </div>
                        <div>
                            <h1 class="text-4xl font-bold text-white mb-2">AI Facebook Graph Search</h1>
                            <p class="text-blue-100 text-lg">Advanced Facebook profile discovery and search optimization</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-4">
                        <div class="bg-white/20 backdrop-blur-sm rounded-lg px-4 py-2 text-white">
                            <i class="fas fa-crown text-yellow-300 mr-2"></i>
                            <span class="text-sm font-medium">PROFESSIONAL</span>
                            <span class="text-xs ml-2 opacity-75">31 DAYS LEFT</span>
                        </div>
                        <button class="bg-white/20 backdrop-blur-sm rounded-lg p-3 text-white hover:bg-white/30 transition-all">
                            <i class="fas fa-cog"></i>
                        </button>
                        <button class="bg-white/20 backdrop-blur-sm rounded-lg p-3 text-white hover:bg-white/30 transition-all">
                            <i class="fas fa-chart-bar"></i>
                        </button>
                    </div>
                </div>

                <!-- Quick Action Buttons -->
                <div class="mt-8 flex flex-wrap gap-3">
                    <button class="bg-white/20 backdrop-blur-sm rounded-lg px-4 py-2 text-white hover:bg-white/30 transition-all">
                        <i class="fas fa-star mr-2"></i>Favorites
                    </button>
                    <button class="bg-white/20 backdrop-blur-sm rounded-lg px-4 py-2 text-white hover:bg-white/30 transition-all">
                        <i class="fas fa-book mr-2"></i>Manual
                    </button>
                    <button class="bg-white/20 backdrop-blur-sm rounded-lg px-4 py-2 text-white hover:bg-white/30 transition-all">
                        <i class="fas fa-key mr-2"></i>License
                    </button>
                    <button class="bg-white/20 backdrop-blur-sm rounded-lg px-4 py-2 text-white hover:bg-white/30 transition-all">
                        <i class="fas fa-chart-line mr-2"></i>Analytics
                    </button>

                    <!-- Keyboard Shortcuts -->
                    <div class="ml-auto flex items-center space-x-2">
                        <span class="keyboard-shortcut">CTRL</span>
                        <span class="keyboard-shortcut">Enter</span>
                        <span class="text-white/70 text-sm">Search</span>

                        <span class="keyboard-shortcut ml-4">CTRL</span>
                        <span class="keyboard-shortcut">B</span>
                        <span class="text-white/70 text-sm">Bulk</span>

                        <span class="keyboard-shortcut ml-4">CTRL</span>
                        <span class="keyboard-shortcut">V</span>
                        <span class="text-white/70 text-sm">Visual</span>

                        <span class="keyboard-shortcut ml-4">F1</span>
                        <span class="text-white/70 text-sm">Settings</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- AI-Powered Search Suggestions -->
        <div class="glass-card rounded-xl p-6 mb-8">
            <div class="flex items-center justify-between mb-6">
                <div class="flex items-center">
                    <i class="fas fa-magic text-purple-500 text-xl mr-3"></i>
                    <h2 class="text-xl font-semibold text-gray-800">AI-Powered Search Suggestions</h2>
                </div>
                <button class="text-blue-500 hover:text-blue-600 transition-colors">
                    <i class="fas fa-sync-alt mr-2"></i>Refresh
                </button>
            </div>

            <!-- Trending Interests -->
            <div class="mb-6">
                <div class="flex items-center mb-3">
                    <i class="fas fa-fire text-orange-500 mr-2"></i>
                    <span class="font-medium text-gray-700">Trending Interests</span>
                </div>
                <div class="flex flex-wrap gap-2">
                    <span class="trending-tag px-3 py-1 rounded-full text-sm font-medium">Photography</span>
                    <span class="trending-tag px-3 py-1 rounded-full text-sm font-medium">Travel</span>
                    <span class="trending-tag px-3 py-1 rounded-full text-sm font-medium">Music</span>
                    <span class="trending-tag px-3 py-1 rounded-full text-sm font-medium">Fitness</span>
                    <span class="trending-tag px-3 py-1 rounded-full text-sm font-medium">Cooking</span>
                    <span class="trending-tag px-3 py-1 rounded-full text-sm font-medium">Gaming</span>
                    <span class="trending-tag px-3 py-1 rounded-full text-sm font-medium">Art & Design</span>
                    <span class="trending-tag px-3 py-1 rounded-full text-sm font-medium">Sports</span>
                </div>
            </div>
        </div>

        <!-- Profile Enhancement Preview -->
        <div class="glass-card rounded-xl p-6 mb-8">
            <div class="flex items-center justify-between mb-6">
                <div class="flex items-center">
                    <i class="fas fa-users text-purple-500 text-xl mr-3"></i>
                    <h2 class="text-xl font-semibold text-gray-800">Profile Enhancement Preview</h2>
                </div>
                <div class="flex items-center">
                    <span class="text-sm text-gray-600 mr-3">Enabled</span>
                    <label class="relative inline-flex items-center cursor-pointer">
                        <input type="checkbox" class="sr-only peer" checked>
                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                    </label>
                    <button class="ml-3 text-gray-400 hover:text-gray-600">
                        <i class="fas fa-cog"></i>
                    </button>
                </div>
            </div>

            <p class="text-gray-600 mb-6">Automatically enhance found Facebook profiles with additional information including mutual friends, shared interests, and social connections.</p>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Mutual Friends -->
                <div class="mutual-friends-card rounded-lg p-4">
                    <div class="flex items-center mb-3">
                        <i class="fas fa-user-friends text-blue-500 mr-2"></i>
                        <h3 class="font-semibold text-blue-800">Mutual Friends</h3>
                    </div>
                    <div class="space-y-2">
                        <div class="flex items-center text-sm">
                            <div class="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                            <span>Sarah Johnson (12 mutual)</span>
                        </div>
                        <div class="flex items-center text-sm">
                            <div class="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                            <span>Mike Chen (8 mutual)</span>
                        </div>
                    </div>
                </div>

                <!-- Shared Interests -->
                <div class="shared-interests-card rounded-lg p-4">
                    <div class="flex items-center mb-3">
                        <i class="fas fa-heart text-pink-500 mr-2"></i>
                        <h3 class="font-semibold text-pink-800">Shared Interests</h3>
                    </div>
                    <div class="space-y-2">
                        <div class="flex items-center text-sm">
                            <i class="fas fa-camera text-purple-500 mr-2"></i>
                            <span>Photography</span>
                        </div>
                        <div class="flex items-center text-sm">
                            <i class="fas fa-plane text-blue-500 mr-2"></i>
                            <span>Travel</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Facebook Advanced Search Options -->
        <div class="glass-card rounded-xl p-6 mb-8">
            <div class="flex items-center mb-6">
                <i class="fab fa-facebook text-blue-500 text-xl mr-3"></i>
                <h2 class="text-xl font-semibold text-gray-800">Facebook Advanced Search Options</h2>
                <button class="ml-auto text-gray-400 hover:text-gray-600 transition-colors">
                    <i class="fas fa-chevron-down"></i>
                </button>
            </div>

            <!-- Facebook People Search -->
            <div class="bg-white rounded-lg border border-gray-200 p-6 mb-6">
                <div class="flex items-center mb-4">
                    <div class="bg-blue-100 rounded-lg p-3 mr-4">
                        <i class="fab fa-facebook text-blue-600 text-xl"></i>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-gray-800">Facebook People Search</h3>
                        <p class="text-gray-600 text-sm">Find friends, family, and connections on Facebook</p>
                    </div>
                </div>

                <!-- Search Type Selector -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Select Search Type</label>
                    <select class="modern-input w-full px-4 py-3 rounded-lg border-2 border-gray-200 focus:border-blue-500">
                        <option>Choose what you want to search for on Facebook</option>
                        <option>People by Name</option>
                        <option>People by Location</option>
                        <option>People by Workplace</option>
                        <option>People by Education</option>
                        <option>People by Interests</option>
                    </select>
                </div>

                <!-- Search Fields -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Name</label>
                        <input type="text" placeholder="e.g. John Doe, Sarah Smith"
                               class="modern-input w-full px-4 py-3 rounded-lg border-2 border-gray-200 focus:border-blue-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">About/Interests</label>
                        <input type="text" placeholder="e.g. Love photography, Travel enthusiast, Music lover"
                               class="modern-input w-full px-4 py-3 rounded-lg border-2 border-gray-200 focus:border-blue-500">
                    </div>
                </div>

                <!-- Location Targeting -->
                <div class="bg-purple-50 rounded-lg p-4 mb-6">
                    <div class="flex items-center mb-3">
                        <i class="fas fa-map-marker-alt text-purple-500 mr-2"></i>
                        <h4 class="font-semibold text-purple-800">Location Targeting</h4>
                    </div>
                    <p class="text-purple-700 text-sm mb-4">Narrow down your search by geographic location</p>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-purple-700 mb-2">Country</label>
                            <input type="text" placeholder="e.g. United States, Canada"
                                   class="modern-input w-full px-3 py-2 rounded-lg border-2 border-purple-200 focus:border-purple-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-purple-700 mb-2">State</label>
                            <input type="text" placeholder="e.g. California, New York"
                                   class="modern-input w-full px-3 py-2 rounded-lg border-2 border-purple-200 focus:border-purple-500">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-purple-700 mb-2">City</label>
                            <input type="text" placeholder="e.g. San Francisco, Boston"
                                   class="modern-input w-full px-3 py-2 rounded-lg border-2 border-purple-200 focus:border-purple-500">
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Search Mode Tabs -->
        <div class="glass-card rounded-xl p-6 mb-8">
            <div class="flex items-center justify-between mb-6">
                <h2 class="text-xl font-semibold text-gray-800">Search Settings</h2>
                <div class="flex items-center">
                    <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium mr-3">
                        <i class="fas fa-globe mr-1"></i>UNIVERSAL
                    </span>
                    <button class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-chevron-down"></i>
                    </button>
                </div>
            </div>

            <p class="text-gray-600 mb-6">Configure your search preferences and options</p>

            <!-- Search Mode Buttons -->
            <div class="flex flex-wrap gap-3 mb-8">
                <button class="tab-button active px-6 py-3 rounded-lg font-medium transition-all">
                    <i class="fas fa-search mr-2"></i>Basic Search
                </button>
                <button class="tab-button bg-gray-100 text-gray-700 hover:bg-gray-200 px-6 py-3 rounded-lg font-medium transition-all">
                    <i class="fas fa-layer-group mr-2"></i>Bulk Search
                </button>
                <button class="tab-button bg-gray-100 text-gray-700 hover:bg-gray-200 px-6 py-3 rounded-lg font-medium transition-all">
                    <i class="fas fa-eye mr-2"></i>Visual Query Builder
                </button>
            </div>
        </div>

        <!-- Search History & Saved Searches -->
        <div class="glass-card rounded-xl p-6 mb-8">
            <div class="flex items-center justify-between mb-6">
                <div class="flex items-center">
                    <i class="fas fa-history text-blue-500 text-xl mr-3"></i>
                    <h2 class="text-xl font-semibold text-gray-800">Search History & Saved Searches</h2>
                </div>
                <div class="flex items-center space-x-3">
                    <button class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition-all">
                        <i class="fas fa-save mr-2"></i>Save Current
                    </button>
                    <button class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-all">
                        <i class="fas fa-trash mr-2"></i>Clear History
                    </button>
                </div>
            </div>

            <!-- History Tabs -->
            <div class="flex border-b border-gray-200 mb-6">
                <button class="px-4 py-2 text-blue-600 border-b-2 border-blue-600 font-medium">Recent Searches</button>
                <button class="px-4 py-2 text-gray-500 hover:text-gray-700 font-medium ml-6">Saved Searches</button>
                <button class="px-4 py-2 text-gray-500 hover:text-gray-700 font-medium ml-6">Favorites</button>
            </div>

            <!-- Search History Items -->
            <div class="space-y-3">
                <div class="search-history-item flex items-center justify-between p-4 rounded-lg hover:bg-gray-50">
                    <div class="flex items-center">
                        <i class="fas fa-search text-gray-400 mr-3"></i>
                        <div>
                            <p class="font-medium text-gray-800">people • Emily</p>
                            <p class="text-sm text-gray-500">5/9/2025, 6:38:22 AM</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2">
                        <button class="bg-blue-500 text-white px-3 py-1 rounded text-sm hover:bg-blue-600">Load</button>
                        <button class="bg-red-500 text-white px-3 py-1 rounded text-sm hover:bg-red-600">Remove</button>
                    </div>
                </div>

                <div class="search-history-item flex items-center justify-between p-4 rounded-lg hover:bg-gray-50">
                    <div class="flex items-center">
                        <i class="fas fa-search text-gray-400 mr-3"></i>
                        <div>
                            <p class="font-medium text-gray-800">people • Rhonda</p>
                            <p class="text-sm text-gray-500">5/9/2025, 7:02:03 AM</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2">
                        <button class="bg-blue-500 text-white px-3 py-1 rounded text-sm hover:bg-blue-600">Load</button>
                        <button class="bg-red-500 text-white px-3 py-1 rounded text-sm hover:bg-red-600">Remove</button>
                    </div>
                </div>

                <div class="search-history-item flex items-center justify-between p-4 rounded-lg hover:bg-gray-50">
                    <div class="flex items-center">
                        <i class="fas fa-search text-gray-400 mr-3"></i>
                        <div>
                            <p class="font-medium text-gray-800">people • Rhonda</p>
                            <p class="text-sm text-gray-500">5/9/2025, 7:40:35 AM</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2">
                        <button class="bg-blue-500 text-white px-3 py-1 rounded text-sm hover:bg-blue-600">Load</button>
                        <button class="bg-red-500 text-white px-3 py-1 rounded text-sm hover:bg-red-600">Remove</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex flex-wrap gap-4 justify-center mb-8">
            <button id="generateQueryBtn" class="btn-facebook text-white px-8 py-4 rounded-xl font-semibold text-lg shadow-lg">
                <i class="fas fa-magic mr-3"></i>Generate Query
            </button>
            <button id="resetBtn" class="bg-gray-500 hover:bg-gray-600 text-white px-8 py-4 rounded-xl font-semibold text-lg shadow-lg transition-all">
                <i class="fas fa-undo mr-3"></i>Reset
            </button>
            <button id="exportBtn" class="bg-green-500 hover:bg-green-600 text-white px-8 py-4 rounded-xl font-semibold text-lg shadow-lg transition-all">
                <i class="fas fa-download mr-3"></i>Export
            </button>
            <button id="shareBtn" class="bg-orange-500 hover:bg-orange-600 text-white px-8 py-4 rounded-xl font-semibold text-lg shadow-lg transition-all">
                <i class="fas fa-share mr-3"></i>Share
            </button>
        </div>

        <!-- Results Section (Hidden by default) -->
        <div id="resultsSection" class="glass-card rounded-xl p-6 mb-8 hidden">
            <div class="flex items-center justify-between mb-6">
                <h2 class="text-xl font-semibold text-gray-800">Search Results</h2>
                <div class="flex items-center space-x-3">
                    <span class="text-sm text-gray-600">Found <span id="resultCount">0</span> profiles</span>
                    <button class="bg-blue-500 text-white px-3 py-1 rounded text-sm">Export Results</button>
                </div>
            </div>
            <div id="resultsContainer" class="space-y-4">
                <!-- Results will be populated here -->
            </div>
        </div>
    </div>

    <!-- Loading Modal -->
    <div id="loadingModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-xl p-8 max-w-md w-full mx-4">
            <div class="text-center">
                <div class="loading-spinner mx-auto mb-4"></div>
                <h3 class="text-lg font-semibold mb-2">Processing Search</h3>
                <p class="text-gray-600">Please wait while we search Facebook profiles...</p>
            </div>
        </div>
    </div>

    <!-- Export Modal -->
    <div id="exportModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-xl p-8 max-w-md w-full mx-4">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-lg font-semibold">Export Results</h3>
                <button id="closeExportModal" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="space-y-4">
                <button class="w-full bg-green-500 hover:bg-green-600 text-white py-3 rounded-lg transition-all">
                    <i class="fas fa-file-excel mr-2"></i>Export to Excel
                </button>
                <button class="w-full bg-red-500 hover:bg-red-600 text-white py-3 rounded-lg transition-all">
                    <i class="fas fa-file-pdf mr-2"></i>Export to PDF
                </button>
                <button class="w-full bg-blue-500 hover:bg-blue-600 text-white py-3 rounded-lg transition-all">
                    <i class="fas fa-file-csv mr-2"></i>Export to CSV
                </button>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let searchHistory = JSON.parse(localStorage.getItem('facebookSearchHistory') || '[]');
        let savedSearches = JSON.parse(localStorage.getItem('facebookSavedSearches') || '[]');
        let currentSearchData = null;

        // Initialize the application
        $(document).ready(function() {
            initializeApp();
            loadSearchHistory();
            setupEventListeners();
            setupKeyboardShortcuts();
        });

        function initializeApp() {
            console.log('Facebook Graph Search initialized');

            // Add pulse animation to trending tags
            $('.trending-tag').each(function(index) {
                setTimeout(() => {
                    $(this).addClass('pulse-animation');
                }, index * 100);
            });

            // Initialize tooltips
            $('[data-tooltip]').each(function() {
                $(this).attr('title', $(this).data('tooltip'));
            });
        }

        function setupEventListeners() {
            // Generate Query Button
            $('#generateQueryBtn').on('click', function() {
                generateSearchQuery();
            });

            // Reset Button
            $('#resetBtn').on('click', function() {
                resetForm();
            });

            // Export Button
            $('#exportBtn').on('click', function() {
                $('#exportModal').removeClass('hidden');
            });

            // Share Button
            $('#shareBtn').on('click', function() {
                shareResults();
            });

            // Close Export Modal
            $('#closeExportModal').on('click', function() {
                $('#exportModal').addClass('hidden');
            });

            // Tab buttons
            $('.tab-button').on('click', function() {
                $('.tab-button').removeClass('active').addClass('bg-gray-100 text-gray-700 hover:bg-gray-200');
                $(this).removeClass('bg-gray-100 text-gray-700 hover:bg-gray-200').addClass('active');
            });

            // Trending tags
            $('.trending-tag').on('click', function() {
                const interest = $(this).text();
                $('input[placeholder*="About/Interests"]').val(interest);
                $(this).addClass('bg-blue-500 text-white');
                setTimeout(() => {
                    $(this).removeClass('bg-blue-500 text-white');
                }, 1000);
            });

            // Search history items
            $(document).on('click', '.load-search', function() {
                const searchData = $(this).data('search');
                loadSearch(searchData);
            });

            $(document).on('click', '.remove-search', function() {
                const index = $(this).data('index');
                removeSearchFromHistory(index);
            });
        }

        function setupKeyboardShortcuts() {
            $(document).on('keydown', function(e) {
                if (e.ctrlKey) {
                    switch(e.key) {
                        case 'Enter':
                            e.preventDefault();
                            generateSearchQuery();
                            break;
                        case 'b':
                            e.preventDefault();
                            $('.tab-button').eq(1).click(); // Bulk Search
                            break;
                        case 'v':
                            e.preventDefault();
                            $('.tab-button').eq(2).click(); // Visual Query Builder
                            break;
                    }
                }

                if (e.key === 'F1') {
                    e.preventDefault();
                    openSettings();
                }
            });
        }

        function generateSearchQuery() {
            // Show loading modal
            $('#loadingModal').removeClass('hidden');

            // Collect form data
            const searchData = {
                name: $('input[placeholder*="John Doe"]').val(),
                interests: $('input[placeholder*="About/Interests"]').val(),
                country: $('input[placeholder*="United States"]').val(),
                state: $('input[placeholder*="California"]').val(),
                city: $('input[placeholder*="San Francisco"]').val(),
                searchType: $('select').val(),
                timestamp: new Date().toISOString()
            };

            // Save to search history
            addToSearchHistory(searchData);

            // Simulate search process
            setTimeout(() => {
                $('#loadingModal').addClass('hidden');
                displaySearchResults(searchData);
            }, 2000);
        }

        function displaySearchResults(searchData) {
            // Show results section
            $('#resultsSection').removeClass('hidden');

            // Simulate results
            const mockResults = [
                {
                    name: searchData.name || 'John Doe',
                    location: `${searchData.city || 'San Francisco'}, ${searchData.state || 'CA'}`,
                    mutualFriends: Math.floor(Math.random() * 20),
                    profileUrl: 'https://facebook.com/profile1'
                },
                {
                    name: searchData.name || 'Jane Smith',
                    location: `${searchData.city || 'San Francisco'}, ${searchData.state || 'CA'}`,
                    mutualFriends: Math.floor(Math.random() * 15),
                    profileUrl: 'https://facebook.com/profile2'
                }
            ];

            $('#resultCount').text(mockResults.length);

            const resultsHtml = mockResults.map(result => `
                <div class="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-all">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-4">
                                <i class="fas fa-user text-blue-600"></i>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-800">${result.name}</h4>
                                <p class="text-sm text-gray-600">${result.location}</p>
                                <p class="text-xs text-blue-600">${result.mutualFriends} mutual friends</p>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
                            <button class="bg-blue-500 text-white px-3 py-1 rounded text-sm hover:bg-blue-600">
                                View Profile
                            </button>
                            <button class="bg-gray-500 text-white px-3 py-1 rounded text-sm hover:bg-gray-600">
                                Save
                            </button>
                        </div>
                    </div>
                </div>
            `).join('');

            $('#resultsContainer').html(resultsHtml);

            // Scroll to results
            $('html, body').animate({
                scrollTop: $('#resultsSection').offset().top - 100
            }, 500);
        }

        function addToSearchHistory(searchData) {
            searchHistory.unshift(searchData);
            if (searchHistory.length > 10) {
                searchHistory = searchHistory.slice(0, 10);
            }
            localStorage.setItem('facebookSearchHistory', JSON.stringify(searchHistory));
            loadSearchHistory();
        }

        function loadSearchHistory() {
            const historyContainer = $('.space-y-3');

            if (searchHistory.length === 0) {
                historyContainer.html('<p class="text-gray-500 text-center py-8">No search history yet</p>');
                return;
            }

            const historyHtml = searchHistory.map((search, index) => `
                <div class="search-history-item flex items-center justify-between p-4 rounded-lg hover:bg-gray-50">
                    <div class="flex items-center">
                        <i class="fas fa-search text-gray-400 mr-3"></i>
                        <div>
                            <p class="font-medium text-gray-800">people • ${search.name || 'Unnamed Search'}</p>
                            <p class="text-sm text-gray-500">${new Date(search.timestamp).toLocaleString()}</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-2">
                        <button class="load-search bg-blue-500 text-white px-3 py-1 rounded text-sm hover:bg-blue-600" data-search='${JSON.stringify(search)}'>Load</button>
                        <button class="remove-search bg-red-500 text-white px-3 py-1 rounded text-sm hover:bg-red-600" data-index="${index}">Remove</button>
                    </div>
                </div>
            `).join('');

            historyContainer.html(historyHtml);
        }

        function loadSearch(searchData) {
            const data = JSON.parse(searchData);
            $('input[placeholder*="John Doe"]').val(data.name || '');
            $('input[placeholder*="About/Interests"]').val(data.interests || '');
            $('input[placeholder*="United States"]').val(data.country || '');
            $('input[placeholder*="California"]').val(data.state || '');
            $('input[placeholder*="San Francisco"]').val(data.city || '');

            // Show success message
            showNotification('Search loaded successfully!', 'success');
        }

        function removeSearchFromHistory(index) {
            searchHistory.splice(index, 1);
            localStorage.setItem('facebookSearchHistory', JSON.stringify(searchHistory));
            loadSearchHistory();
            showNotification('Search removed from history', 'info');
        }

        function resetForm() {
            $('input[type="text"]').val('');
            $('select').prop('selectedIndex', 0);
            $('#resultsSection').addClass('hidden');
            showNotification('Form reset successfully!', 'info');
        }

        function shareResults() {
            if (navigator.share) {
                navigator.share({
                    title: 'Facebook Graph Search Results',
                    text: 'Check out these Facebook search results',
                    url: window.location.href
                });
            } else {
                // Fallback: copy to clipboard
                navigator.clipboard.writeText(window.location.href).then(() => {
                    showNotification('Link copied to clipboard!', 'success');
                });
            }
        }

        function openSettings() {
            showNotification('Settings panel coming soon!', 'info');
        }

        function showNotification(message, type = 'info') {
            const colors = {
                success: 'bg-green-500',
                error: 'bg-red-500',
                info: 'bg-blue-500',
                warning: 'bg-yellow-500'
            };

            const notification = $(`
                <div class="fixed top-4 right-4 ${colors[type]} text-white px-6 py-3 rounded-lg shadow-lg z-50 transform translate-x-full transition-transform">
                    <div class="flex items-center">
                        <i class="fas fa-info-circle mr-2"></i>
                        <span>${message}</span>
                    </div>
                </div>
            `);

            $('body').append(notification);

            setTimeout(() => {
                notification.removeClass('translate-x-full');
            }, 100);

            setTimeout(() => {
                notification.addClass('translate-x-full');
                setTimeout(() => {
                    notification.remove();
                }, 300);
            }, 3000);
        }
    </script>
</body>
</html>

