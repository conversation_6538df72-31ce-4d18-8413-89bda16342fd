<?php
// Dynamic Configuration
$protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
$host = $_SERVER['HTTP_HOST'];
$scriptPath = dirname($_SERVER['SCRIPT_NAME']);
$baseUrl = $protocol . '://' . $host . $scriptPath;
$rootUrl = $protocol . '://' . $host;

// API Endpoints
$licenseValidationEndpoint = $baseUrl . '/license_validation.php';
?>
<!DOCTYPE html>
<html lang="en" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Facebook Graph Search</title>
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🔍</text></svg>">
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>
    <script>
        // Dynamic Configuration for JavaScript
        window.APP_CONFIG = {
            baseUrl: '<?php echo $baseUrl; ?>',
            rootUrl: '<?php echo $rootUrl; ?>',
            licenseValidationEndpoint: '<?php echo $licenseValidationEndpoint; ?>',
            facebookSearchBaseUrl: 'https://www.facebook.com/search/',
            whatsappSupportUrl: 'https://wa.me/270618757667?text=Hello!%20I%20need%20assistance%20with%20Facebook%20Graph%20Search.'
        };

        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        facebook: {
                            blue: '#1877F2',
                            darkBlue: '#166FE5'
                        }
                    }
                }
            }
        }
    </script>
    <style>
        .modern-card {
            background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.1);
        }

        .dark .modern-card {
            background: linear-gradient(135deg, rgba(0,0,0,0.2) 0%, rgba(0,0,0,0.1) 100%);
            border: 1px solid rgba(255,255,255,0.1);
        }

        /* Modern Input System - LinkedIn Style */
        .modern-input-group {
            position: relative;
            margin-bottom: 1.5rem;
        }
        .modern-input {
            width: 100%;
            padding: 16px 20px;
            border: 2px solid rgba(229, 231, 235, 0.8);
            border-radius: 12px;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            font-size: 16px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            outline: none;
        }
        .modern-input:focus {
            border-color: #1877F2;
            background: rgba(255, 255, 255, 1);
            box-shadow: 0 0 0 4px rgba(24, 119, 242, 0.1);
            transform: translateY(-2px);
        }
        .modern-input::placeholder {
            color: rgba(107, 114, 128, 0.7);
            transition: all 0.3s ease;
        }
        .modern-input:focus::placeholder {
            opacity: 0;
            transform: translateY(-4px);
        }
        .dark .modern-input {
            background: rgba(55, 65, 81, 0.9);
            border-color: rgba(75, 85, 99, 0.8);
            color: #f3f4f6;
        }
        .dark .modern-input:focus {
            background: rgba(55, 65, 81, 1);
            border-color: #1877F2;
        }

        .modern-btn {
            transition: all 0.3s ease;
            transform: translateY(0);
        }

        .modern-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .modern-btn-primary {
            background: linear-gradient(135deg, #1877F2 0%, #166FE5 100%);
            color: white;
            border: none;
        }

        .modern-btn-secondary {
            background: linear-gradient(135deg, #6B7280 0%, #4B5563 100%);
            color: white;
            border: none;
        }

        /* Floating Labels - LinkedIn Style */
        .floating-label {
            position: absolute;
            left: 20px;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(255, 255, 255, 0.9);
            padding: 0 8px;
            color: rgba(107, 114, 128, 0.8);
            font-size: 16px;
            pointer-events: none;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: 4px;
            z-index: 10;
        }
        .modern-input:focus + .floating-label,
        .modern-input:not(:placeholder-shown) + .floating-label,
        .modern-input:valid + .floating-label,
        select.modern-input:not([value=""]) + .floating-label {
            top: 0;
            font-size: 12px;
            color: #1877F2;
            font-weight: 500;
        }
        .dark .floating-label {
            background: rgba(55, 65, 81, 0.9);
            color: rgba(156, 163, 175, 0.8);
        }
        .dark .modern-input:focus + .floating-label,
        .dark .modern-input:not(:placeholder-shown) + .floating-label,
        .dark .modern-input:valid + .floating-label,
        .dark select.modern-input:not([value=""]) + .floating-label {
            color: #1877F2;
        }

        /* Select specific styling */
        select.modern-input {
            cursor: pointer;
        }
        select.modern-input option {
            background: white;
            color: #374151;
        }
        .dark select.modern-input option {
            background: #374151;
            color: #f3f4f6;
        }

        .modern-input-group {
            position: relative;
            margin-bottom: 1.5rem;
        }

        .modern-section-header {
            display: flex;
            align-items: center;
            margin-bottom: 2rem;
        }

        .section-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #1877F2 0%, #166FE5 100%);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            color: white;
            font-size: 24px;
        }

        .modern-section-header h3 {
            font-size: 28px;
            font-weight: 700;
            color: #1F2937;
            margin: 0;
        }

        .dark .modern-section-header h3 {
            color: #F9FAFB;
        }

        .modern-tab {
            padding: 12px 24px;
            border-radius: 12px;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            font-weight: 500;
        }

        .modern-tab.active {
            background: linear-gradient(135deg, #1877F2 0%, #166FE5 100%);
            color: white;
            border-color: #1877F2;
        }

        .modern-tab:not(.active) {
            background: rgba(107, 114, 128, 0.1);
            color: #6B7280;
        }

        .modern-tab:not(.active):hover {
            background: rgba(24, 119, 242, 0.1);
            color: #1877F2;
        }

        .whatsapp-chat-btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #25D366 0%, #128C7E 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            text-decoration: none;
            box-shadow: 0 4px 20px rgba(37, 211, 102, 0.4);
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .whatsapp-chat-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 25px rgba(37, 211, 102, 0.6);
        }

        .whatsapp-chat-tooltip {
            position: absolute;
            right: 70px;
            top: 50%;
            transform: translateY(-50%);
            background: #333;
            color: white;
            padding: 8px 12px;
            border-radius: 8px;
            font-size: 14px;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .whatsapp-chat-btn:hover .whatsapp-chat-tooltip {
            opacity: 1;
            visibility: visible;
        }

        .template-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .history-tab {
            transition: all 0.3s ease;
        }

        .history-tab.active {
            border-color: #1877F2 !important;
            color: #1877F2 !important;
        }

        /* Keyboard Shortcuts Styling */
        .keyboard-shortcut-modern {
            display: inline-flex;
            align-items: center;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 6px;
            padding: 4px 8px;
            font-size: 11px;
            color: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(4px);
            transition: all 0.2s ease;
        }
        .keyboard-shortcut-modern:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.3);
            transform: translateY(-1px);
        }
        .keyboard-shortcut-modern kbd {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 3px;
            padding: 2px 4px;
            font-size: 10px;
            font-family: monospace;
            font-weight: 600;
            color: white;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        /* Modern Tabs Styling */
        .modern-tabs {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
            margin-bottom: 2rem;
            justify-content: center;
        }

        .modern-tab {
            display: inline-flex;
            align-items: center;
            padding: 12px 24px;
            background: rgba(107, 114, 128, 0.1);
            color: #6B7280;
            border: 2px solid transparent;
            border-radius: 12px;
            font-weight: 500;
            transition: all 0.3s ease;
            cursor: pointer;
            text-decoration: none;
        }

        .modern-tab:hover {
            background: rgba(24, 119, 242, 0.1);
            color: #1877F2;
            transform: translateY(-2px);
        }

        .modern-tab.active {
            background: linear-gradient(135deg, #1877F2 0%, #166FE5 100%);
            color: white;
            border-color: #1877F2;
            box-shadow: 0 4px 15px rgba(24, 119, 242, 0.3);
        }

        /* Tab Content */
        .tab-content {
            animation: fadeIn 0.3s ease-in-out;
        }

        .tab-content.hidden {
            display: none;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* License Action Buttons */
        .license-action-btn {
            transition: all 0.3s ease;
            border: none;
            outline: none;
            position: relative;
            overflow: hidden;
        }

        .license-action-btn:not(.cursor-not-allowed):hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .license-action-btn:not(.cursor-not-allowed):active {
            transform: translateY(0);
        }

        /* Template Cards */
        .template-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        /* Header Gradient */
        .header-gradient {
            background: linear-gradient(135deg, #1877F2 0%, #166FE5 50%, #0066cc 100%);
            background-size: 200% 200%;
            animation: gradientShift 8s ease infinite;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .dark .header-gradient {
            background: linear-gradient(135deg, #1f2937 0%, #374151 50%, #4b5563 100%);
            background-size: 200% 200%;
        }

        /* Header Logo Icon */
        .header-logo-icon {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        .header-logo-icon:hover {
            transform: rotate(5deg) scale(1.1);
            box-shadow: 0 8px 16px rgba(24, 119, 242, 0.3);
        }

        /* License Status */
        .license-status {
            display: inline-flex;
            align-items: center;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        .license-trial {
            background: rgba(251, 191, 36, 0.2);
            color: #f59e0b;
            border: 1px solid rgba(251, 191, 36, 0.3);
        }

        /* Keyboard Shortcuts */
        .keyboard-shortcut-modern {
            display: inline-flex;
            align-items: center;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 6px;
            padding: 4px 8px;
            font-size: 11px;
            color: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
        }
        .keyboard-shortcut-modern kbd {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 3px;
            padding: 2px 4px;
            font-size: 10px;
            font-family: monospace;
            margin: 0 1px;
        }

        /* ===== MODERN UI/UX DESIGN SYSTEM ===== */

        /* Modern Card System */
        .modern-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }
        .modern-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
        }
        .modern-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
            border-color: rgba(24, 119, 242, 0.3);
        }
        .dark .modern-card {
            background: rgba(31, 41, 55, 0.95);
            border-color: rgba(75, 85, 99, 0.3);
        }
        .dark .modern-card:hover {
            border-color: rgba(24, 119, 242, 0.5);
        }

        /* LinkedIn-style Tab System */
        .linkedin-tab {
            background: transparent;
            color: #666;
            border: none;
            font-size: 14px;
            font-weight: 500;
        }

        .linkedin-tab.active {
            background: #0a66c2;
            color: white;
            box-shadow: 0 2px 8px rgba(10, 102, 194, 0.3);
        }

        .linkedin-tab:hover:not(.active) {
            background: rgba(10, 102, 194, 0.1);
            color: #0a66c2;
        }

        /* LinkedIn-style Input Groups */
        .linkedin-input-group {
            margin-bottom: 1.5rem;
        }

        .linkedin-label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: #0a66c2;
            margin-bottom: 8px;
        }

        .linkedin-input, .linkedin-select {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            background: white;
            transition: all 0.2s ease;
        }

        .linkedin-input:focus, .linkedin-select:focus {
            outline: none;
            border-color: #0a66c2;
            box-shadow: 0 0 0 2px rgba(10, 102, 194, 0.1);
        }

        .linkedin-input.valid {
            border-color: #057642;
        }

        .name-validation {
            transition: all 0.3s ease;
        }

        .name-validation.hidden {
            display: none;
        }

        /* LinkedIn-style Location Cards */
        .linkedin-location-card {
            background: #f3f2ef;
            border-radius: 8px;
            padding: 16px;
            border: 1px solid #e1e5e9;
        }

        .linkedin-location-label {
            display: block;
            font-size: 12px;
            font-weight: 500;
            color: #666;
            margin-bottom: 8px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .linkedin-location-input {
            width: 100%;
            padding: 8px 0;
            border: none;
            background: transparent;
            font-size: 16px;
            color: #333;
            border-bottom: 1px solid transparent;
        }

        .linkedin-location-input:focus {
            outline: none;
            border-bottom-color: #0a66c2;
        }

        .linkedin-location-input::placeholder {
            color: #999;
            font-size: 14px;
        }

        /* Dark mode styles for LinkedIn components */
        .dark .linkedin-tab {
            color: #9ca3af;
        }

        .dark .linkedin-tab.active {
            background: #0a66c2;
            color: white;
        }

        .dark .linkedin-label {
            color: #60a5fa;
        }

        .dark .linkedin-input, .dark .linkedin-select {
            background: #374151;
            border-color: #4b5563;
            color: white;
        }

        .dark .linkedin-input:focus, .dark .linkedin-select:focus {
            border-color: #60a5fa;
            box-shadow: 0 0 0 2px rgba(96, 165, 250, 0.1);
        }

        /* Beautiful Bulk Search Styles */
        .beautiful-section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            background: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(248,250,252,0.9) 100%);
            border: 1px solid rgba(226, 232, 240, 0.8);
            border-radius: 1rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            transition: all 0.3s ease;
        }

        .beautiful-section:hover {
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            transform: translateY(-2px);
        }

        .dark .beautiful-section {
            background: linear-gradient(135deg, rgba(31, 41, 55, 0.9) 0%, rgba(17, 24, 39, 0.9) 100%);
            border-color: rgba(75, 85, 99, 0.8);
        }

        .beautiful-toggle-btn {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.8);
        }

        .dark .beautiful-toggle-btn {
            background: rgba(31, 41, 55, 0.8);
        }

        .beautiful-upload-area {
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .beautiful-upload-area:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        .beautiful-upload-btn {
            backdrop-filter: blur(10px);
        }

        .beautiful-textarea {
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .beautiful-textarea:focus {
            transform: translateY(-2px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        .beautiful-matrix-info {
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .beautiful-matrix-info:hover {
            transform: translateY(-2px);
        }

        .beautiful-generate-btn {
            backdrop-filter: blur(10px);
            position: relative;
            overflow: hidden;
        }

        .beautiful-generate-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .beautiful-generate-btn:hover::before {
            left: 100%;
        }

        .beautiful-clear-btn {
            backdrop-filter: blur(10px);
        }

        .dark .linkedin-location-card {
            background: #374151;
            border-color: #4b5563;
        }

        .dark .linkedin-location-label {
            color: #9ca3af;
        }

        .dark .linkedin-location-input {
            color: white;
        }

        .dark .linkedin-location-input::placeholder {
            color: #6b7280;
        }

        /* Beautiful Form Styles */
        .beautiful-input-group {
            margin-bottom: 2rem;
            position: relative;
        }

        .beautiful-input, .beautiful-select {
            width: 100%;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.95));
            backdrop-filter: blur(12px);
            border: 2px solid rgba(209, 213, 219, 0.6);
            border-radius: 16px;
            font-size: 16px;
            font-weight: 500;
            color: #1f2937;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            z-index: 1;
            box-shadow: 0 4px 24px rgba(0, 0, 0, 0.06), 0 2px 8px rgba(0, 0, 0, 0.04);
        }

        .beautiful-input:hover, .beautiful-select:hover {
            border-color: rgba(24, 119, 242, 0.4);
            box-shadow: 0 6px 28px rgba(24, 119, 242, 0.08), 0 2px 12px rgba(24, 119, 242, 0.06);
            transform: translateY(-1px);
        }

        .beautiful-input:focus, .beautiful-select:focus {
            outline: none;
            border-color: rgba(24, 119, 242, 0.8);
            box-shadow: 0 0 0 4px rgba(24, 119, 242, 0.12), 0 8px 32px rgba(24, 119, 242, 0.16);
            transform: translateY(-2px);
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.98), rgba(248, 250, 252, 0.98));
        }

        .beautiful-floating-label {
            position: absolute;
            left: 48px;
            top: 50%;
            transform: translateY(-50%);
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.98), rgba(249, 250, 251, 0.98));
            padding: 4px 12px;
            font-size: 16px;
            font-weight: 600;
            color: #374151;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            pointer-events: none;
            border-radius: 8px;
            backdrop-filter: blur(12px);
            border: 1px solid rgba(209, 213, 219, 0.5);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            z-index: 10;
        }

        .beautiful-input:focus + .beautiful-floating-label,
        .beautiful-input:not(:placeholder-shown) + .beautiful-floating-label {
            top: -8px;
            left: 44px;
            font-size: 13px;
            font-weight: 700;
            color: #1877f2;
            background: linear-gradient(135deg, rgba(24, 119, 242, 0.1), rgba(66, 165, 245, 0.1));
            border: 1px solid rgba(24, 119, 242, 0.3);
            box-shadow: 0 4px 12px rgba(24, 119, 242, 0.15);
            transform: translateY(0);
        }

        .beautiful-label {
            position: absolute;
            top: 0;
            left: 44px;
            font-size: 12px;
            font-weight: 600;
            color: #3b82f6;
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(147, 197, 253, 0.1));
            border: 1px solid rgba(59, 130, 246, 0.2);
            padding: 0 8px;
            border-radius: 6px;
            backdrop-filter: blur(10px);
        }

        .beautiful-hint {
            margin-top: 8px;
            font-size: 14px;
            color: #6b7280;
            font-weight: 500;
            padding-left: 4px;
        }

        /* Dark mode styles for beautiful forms */
        .dark .beautiful-input, .dark .beautiful-select {
            background: rgba(31, 41, 55, 0.9);
            border-color: rgba(75, 85, 99, 0.8);
            color: #f9fafb;
        }

        .dark .beautiful-input:hover, .dark .beautiful-select:hover {
            border-color: rgba(96, 165, 250, 0.8);
            box-shadow: 0 4px 20px rgba(59, 130, 246, 0.2);
        }

        .dark .beautiful-input:hover, .dark .beautiful-select:hover {
            border-color: rgba(66, 165, 245, 0.5);
            box-shadow: 0 6px 28px rgba(66, 165, 245, 0.12), 0 2px 12px rgba(66, 165, 245, 0.08);
            transform: translateY(-1px);
        }

        .dark .beautiful-input:focus, .dark .beautiful-select:focus {
            border-color: rgba(66, 165, 245, 0.8);
            box-shadow: 0 0 0 4px rgba(66, 165, 245, 0.2), 0 8px 32px rgba(66, 165, 245, 0.25);
            transform: translateY(-2px);
        }

        .dark .beautiful-floating-label {
            background: linear-gradient(135deg, rgba(31, 41, 55, 0.98), rgba(17, 24, 39, 0.98));
            color: #d1d5db;
            border-color: rgba(75, 85, 99, 0.5);
        }

        .dark .beautiful-input:focus + .beautiful-floating-label,
        .dark .beautiful-input:not(:placeholder-shown) + .beautiful-floating-label {
            color: #42a5f5;
            background: linear-gradient(135deg, rgba(66, 165, 245, 0.2), rgba(144, 202, 249, 0.2));
            border-color: rgba(66, 165, 245, 0.4);
            box-shadow: 0 4px 12px rgba(66, 165, 245, 0.2);
        }

        .dark .beautiful-label {
            color: #60a5fa;
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(147, 197, 253, 0.2));
            border-color: rgba(59, 130, 246, 0.3);
        }

        .dark .beautiful-hint {
            color: #9ca3af;
        }

        /* Custom Scrollbar Styles */
        .scrollbar-thin {
            scrollbar-width: thin;
        }

        .scrollbar-thin::-webkit-scrollbar {
            width: 8px;
        }

        .scrollbar-thin::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 4px;
        }

        .scrollbar-thin::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 4px;
            border: 1px solid #f1f5f9;
        }

        .scrollbar-thin::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }

        .dark .scrollbar-thin::-webkit-scrollbar-track {
            background: #374151;
        }

        .dark .scrollbar-thin::-webkit-scrollbar-thumb {
            background: #6b7280;
            border-color: #374151;
        }

        .dark .scrollbar-thin::-webkit-scrollbar-thumb:hover {
            background: #9ca3af;
        }

        /* Beautiful Button Styles */
        .beautiful-button-primary, .beautiful-button-secondary {
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(10px);
        }

        .beautiful-button-primary::before, .beautiful-button-secondary::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .beautiful-button-primary:hover::before, .beautiful-button-secondary:hover::before {
            left: 100%;
        }

        /* Autocomplete Suggestions Styling */
        .autocomplete-suggestions {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 2px solid rgba(59, 130, 246, 0.2);
            border-top: none;
            border-radius: 0 0 12px 12px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            z-index: 1000;
            max-height: 200px;
            overflow-y: auto;
            backdrop-filter: blur(10px);
        }

        .autocomplete-suggestions.hidden {
            display: none;
        }

        .autocomplete-suggestion {
            padding: 12px 16px;
            cursor: pointer;
            border-bottom: 1px solid rgba(229, 231, 235, 0.5);
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
        }

        .autocomplete-suggestion:last-child {
            border-bottom: none;
        }

        .autocomplete-suggestion:hover {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(147, 197, 253, 0.1));
            color: #1e40af;
        }

        .autocomplete-suggestion.active {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.15), rgba(147, 197, 253, 0.15));
            color: #1e40af;
        }

        .autocomplete-suggestion i {
            margin-right: 8px;
            color: #6b7280;
        }

        .autocomplete-suggestion:hover i {
            color: #3b82f6;
        }

        /* Dark mode autocomplete */
        .dark .autocomplete-suggestions {
            background: rgba(31, 41, 55, 0.95);
            border-color: rgba(75, 85, 99, 0.5);
        }

        .dark .autocomplete-suggestion {
            border-bottom-color: rgba(75, 85, 99, 0.3);
            color: #f9fafb;
        }

        .dark .autocomplete-suggestion:hover {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(147, 197, 253, 0.2));
            color: #60a5fa;
        }

        .dark .autocomplete-suggestion.active {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.25), rgba(147, 197, 253, 0.25));
            color: #60a5fa;
        }

        /* Modern Section Headers */
        .modern-section-header {
            display: flex;
            align-items: center;
            padding: 20px 24px;
            background: linear-gradient(135deg, rgba(24, 119, 242, 0.05), rgba(24, 119, 242, 0.02));
            border-radius: 16px 16px 0 0;
            border-bottom: 1px solid rgba(229, 231, 235, 0.5);
            margin: -1px -1px 0 -1px;
        }
        .modern-section-header h3 {
            font-size: 18px;
            font-weight: 700;
            color: #1f2937;
            margin: 0;
        }
        .modern-section-header .section-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            background: linear-gradient(135deg, #1877F2, #166FE5);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            margin-right: 16px;
            box-shadow: 0 4px 12px rgba(24, 119, 242, 0.3);
        }
        .dark .modern-section-header {
            background: linear-gradient(135deg, rgba(55, 65, 81, 0.3), rgba(55, 65, 81, 0.1));
            border-bottom-color: rgba(75, 85, 99, 0.5);
        }
        .dark .modern-section-header h3 {
            color: #f3f4f6;
        }

        /* Modern Badge System */
        .modern-badge {
            display: inline-flex;
            align-items: center;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        .modern-badge-info {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
        }

        /* Query Builder Styles */
        .query-builder {
            min-height: 200px;
            border: 2px dashed #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            background: #f9fafb;
            transition: all 0.3s ease;
        }

        .query-builder.dark {
            background: #374151;
            border-color: #6b7280;
        }

        .query-builder-empty {
            text-align: center;
            color: #9ca3af;
            padding: 40px 20px;
        }

        .query-block {
            display: inline-block;
            background: #3b82f6;
            color: white;
            padding: 8px 12px;
            margin: 4px;
            border-radius: 6px;
            cursor: move;
            position: relative;
            transition: all 0.2s ease;
        }

        .query-block:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }

        .query-block .remove-btn {
            position: absolute;
            top: -8px;
            right: -8px;
            background: #ef4444;
            color: white;
            border: none;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 12px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .query-block .remove-btn:hover {
            background: #dc2626;
        }

        /* Search Engine Buttons */
        .search-engine-btn {
            display: flex;
            align-items: center;
            padding: 16px;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            background: white;
            color: #374151;
            transition: all 0.2s ease;
            cursor: pointer;
        }

        .search-engine-btn:hover {
            border-color: #3b82f6;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
        }

        .search-engine-btn.active {
            border-color: #3b82f6;
            background: #eff6ff;
            color: #1e40af;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
        }

        .dark .search-engine-btn {
            background: #374151;
            border-color: #6b7280;
            color: #f9fafb;
        }

        .dark .search-engine-btn.active {
            background: #1e3a8a;
            border-color: #3b82f6;
            color: #dbeafe;
        }

        /* Exclude Suggestion Buttons */
        .exclude-suggestion-btn {
            padding: 4px 8px;
            background: #f3f4f6;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 12px;
            color: #6b7280;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .exclude-suggestion-btn:hover {
            background: #e5e7eb;
            color: #374151;
        }

        .dark .exclude-suggestion-btn {
            background: #4b5563;
            border-color: #6b7280;
            color: #d1d5db;
        }

        .dark .exclude-suggestion-btn:hover {
            background: #6b7280;
            color: #f3f4f6;
        }

        /* Form Field Styling */
        .form-field {
            position: relative;
            margin-bottom: 20px;
        }

        .form-field input,
        .form-field select,
        .form-field textarea {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            background: white;
            font-size: 14px;
            transition: all 0.2s ease;
        }

        .form-field input:focus,
        .form-field select:focus,
        .form-field textarea:focus {
            outline: none;
            border-color: #1877F2;
            box-shadow: 0 0 0 3px rgba(24, 119, 242, 0.1);
        }

        .form-field label {
            position: absolute;
            top: -8px;
            left: 12px;
            background: white;
            padding: 0 4px;
            font-size: 12px;
            font-weight: 500;
            color: #6b7280;
        }

        .dark .form-field input,
        .dark .form-field select,
        .dark .form-field textarea {
            background: #374151;
            border-color: #6b7280;
            color: white;
        }

        .dark .form-field label {
            background: #374151;
            color: #d1d5db;
        }

        /* Grid Layout for Form Fields */
        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr;
            }
        }

        /* Orange slider styling for Facebook enrichment settings */
        .slider-orange::-webkit-slider-thumb {
            appearance: none;
            height: 20px;
            width: 20px;
            border-radius: 50%;
            background: #ea580c;
            cursor: pointer;
            border: 2px solid #fff;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .slider-orange::-moz-range-thumb {
            height: 20px;
            width: 20px;
            border-radius: 50%;
            background: #ea580c;
            cursor: pointer;
            border: 2px solid #fff;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }
    </style>
</head>
<body class="bg-gray-100 dark:bg-gray-900 text-gray-800 dark:text-gray-200">
    <div class="container mx-auto p-6 sm:p-8 max-w-5xl mt-6 sm:mt-12 bg-gradient-to-br from-gray-50 to-blue-50 dark:from-gray-900 dark:to-gray-800 rounded-2xl shadow-2xl border border-white/20">
        <!-- Enhanced Header Section -->
        <div class="header-gradient dark:from-gray-800 dark:to-gray-700 rounded-xl shadow-lg mb-8 overflow-hidden">
            <div class="px-6 py-8">
                <!-- Top Row: Title and Theme Toggle -->
                <div class="flex flex-col lg:flex-row justify-between items-start lg:items-center mb-6">
                    <div class="flex items-center mb-4 lg:mb-0">
                        <div class="header-logo-icon bg-white dark:bg-gray-800 rounded-full p-3 mr-4 shadow-md">
                            <i class="fab fa-facebook text-2xl text-facebook-blue"></i>
                        </div>
                        <div>
                            <h1 class="text-3xl lg:text-4xl font-bold text-white mb-1">AI Facebook Graph Search</h1>
                            <p class="text-blue-100 dark:text-gray-300 text-sm">Advanced Facebook profile discovery and search optimization</p>
                        </div>
                    </div>

                    <!-- Theme Toggle -->
                    <div class="flex items-center bg-white/10 dark:bg-gray-700/50 rounded-full px-4 py-2 backdrop-blur-sm">
                        <i class="fas fa-moon text-white mr-2"></i>
                        <div class="relative inline-block w-12 mr-2 align-middle select-none transition duration-200 ease-in">
                            <input type="checkbox" name="toggle" id="toggle" class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-2 border-blue-300 appearance-none cursor-pointer transition-all duration-200"/>
                            <label for="toggle" class="toggle-label block overflow-hidden h-6 rounded-full bg-blue-300 cursor-pointer transition-all duration-200"></label>
                        </div>
                        <i class="fas fa-sun text-white"></i>
                    </div>
                </div>

                <!-- Bottom Row: Action Buttons and Shortcuts -->
                <div class="flex flex-col xl:flex-row justify-between items-start xl:items-center space-y-4 xl:space-y-0">
                    <!-- Action Buttons -->
                    <div class="flex flex-wrap items-center gap-3">
                        <!-- License Status -->
                        <div id="licenseStatus" class="license-status license-trial bg-white/20 backdrop-blur-sm border border-white/30">
                            <i class="fas fa-key mr-2"></i>
                            <span id="licenseText">Trial: 7 days left</span>
                        </div>

                        <!-- Enhanced Action Buttons -->
                        <button id="manageLicense" class="bg-white/20 hover:bg-white/30 backdrop-blur-sm text-white border border-white/30 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 hover:scale-105 hover:shadow-lg">
                            <i class="fas fa-cog mr-2"></i>License
                        </button>

                        <button id="toggleAnalytics" class="bg-white/20 hover:bg-white/30 backdrop-blur-sm text-white border border-white/30 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 hover:scale-105 hover:shadow-lg">
                            <i class="fas fa-chart-line mr-2"></i>Analytics
                        </button>

                        <button id="toggleFavorites" class="bg-white/20 hover:bg-white/30 backdrop-blur-sm text-white border border-white/30 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 hover:scale-105 hover:shadow-lg">
                            <i class="fas fa-star mr-2"></i>Favorites
                        </button>
                    </div>

                    <!-- Keyboard Shortcuts -->
                    <div class="bg-white/10 dark:bg-gray-700/30 backdrop-blur-sm rounded-lg px-4 py-3 border border-white/20">
                        <div class="text-xs text-white/90 font-medium mb-2">
                            <i class="fas fa-keyboard mr-1"></i>Keyboard Shortcuts
                        </div>
                        <div class="flex flex-wrap gap-2">
                            <span class="keyboard-shortcut-modern">
                                <kbd>Ctrl</kbd>+<kbd>Enter</kbd>
                                <span class="ml-1 text-xs">Search</span>
                            </span>
                            <span class="keyboard-shortcut-modern">
                                <kbd>Ctrl</kbd>+<kbd>R</kbd>
                                <span class="ml-1 text-xs">Reset</span>
                            </span>
                            <span class="keyboard-shortcut-modern">
                                <kbd>Ctrl</kbd>+<kbd>S</kbd>
                                <span class="ml-1 text-xs">Settings</span>
                            </span>
                            <span class="keyboard-shortcut-modern">
                                <kbd>Ctrl</kbd>+<kbd>B</kbd>
                                <span class="ml-1 text-xs">Bulk</span>
                            </span>
                            <span class="keyboard-shortcut-modern">
                                <kbd>Ctrl</kbd>+<kbd>Q</kbd>
                                <span class="ml-1 text-xs">Visual</span>
                            </span>
                            <span class="keyboard-shortcut-modern">
                                <kbd>Ctrl</kbd>+<kbd>T</kbd>
                                <span class="ml-1 text-xs">Templates</span>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Error/Success Messages -->
        <div id="globalMessages" class="mb-4"></div>

        <!-- Modern Search Settings Panel -->
        <div class="container mx-auto max-w-5xl">
            <div class="mb-8 modern-card">
            <div class="modern-section-header">
                <div class="section-icon">
                    <i class="fas fa-cog"></i>
                </div>
                <div class="flex-1">
                    <h3>Search Settings</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">Configure your search preferences and options</p>
                </div>
                <div class="flex items-center space-x-3">
                    <span class="modern-badge modern-badge-info">
                        <i class="fas fa-globe mr-1"></i>Universal
                    </span>
                    <button type="button" id="toggleSearchSettings" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors">
                        <i class="fas fa-chevron-down transition-transform duration-200" id="settingsChevron"></i>
                    </button>
                </div>
            </div>

            <div id="searchSettingsContent" class="hidden p-6 space-y-6">
                <!-- Facebook Search Scope -->
                <div class="modern-card p-6">
                    <div class="flex items-center mb-4">
                        <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mr-3">
                            <i class="fab fa-facebook text-white"></i>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-800 dark:text-gray-200">Facebook Search Scope</h4>
                            <p class="text-sm text-gray-600 dark:text-gray-400">Configure your Facebook search preferences</p>
                        </div>
                    </div>
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Search Privacy Level</label>
                            <select id="privacyLevel" class="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">
                                <option value="public">Public profiles only</option>
                                <option value="all" selected>All accessible profiles</option>
                                <option value="friends">Friends and friends of friends</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Location Radius</label>
                            <select id="locationRadius" class="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100">
                                <option value="">Any distance</option>
                                <option value="1">Within 1 mile</option>
                                <option value="5">Within 5 miles</option>
                                <option value="10">Within 10 miles</option>
                                <option value="25">Within 25 miles</option>
                                <option value="50">Within 50 miles</option>
                                <option value="100">Within 100 miles</option>
                            </select>
                        </div>
                    </div>
                </div>

                    <!-- Exclude Terms Management -->
                    <div class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-600">
                        <div class="flex items-center justify-between mb-3">
                            <h4 class="font-semibold text-gray-800 dark:text-gray-200">
                                <i class="fas fa-minus-circle mr-2 text-red-500"></i>Exclude Terms
                            </h4>
                            <div class="flex items-center space-x-2">
                                <span class="text-xs text-gray-500 dark:text-gray-400" id="excludeCounterSettings">0/10 terms</span>
                                <button type="button" id="clearAllExcludesSettings" class="text-xs text-red-500 hover:text-red-700 hidden" title="Clear all exclude terms">
                                    <i class="fas fa-trash mr-1"></i>Clear All
                                </button>
                            </div>
                        </div>

                        <div class="relative mb-3">
                            <input type="text" id="negativeSettings"
                                   placeholder="Type and press Enter to add terms (e.g. recruiter, student, intern)"
                                   class="w-full p-3 pr-12 border rounded-lg dark:bg-gray-700 dark:border-gray-600 focus:ring-2 focus:ring-red-500 focus:border-red-500 transition-all">
                            <div class="absolute right-3 top-1/2 transform -translate-y-1/2">
                                <button type="button" id="addExcludeTermSettings" class="text-gray-400 hover:text-red-500 transition-colors" title="Add exclude term">
                                    <i class="fas fa-plus-circle"></i>
                                </button>
                            </div>
                        </div>

                        <!-- Exclude Terms Display -->
                        <div id="excludeTagsSettings" class="min-h-[40px] p-2 border border-gray-200 dark:border-gray-600 rounded bg-gray-50 dark:bg-gray-700">
                            <div class="text-xs text-gray-500 dark:text-gray-400">No exclude terms added</div>
                        </div>

                        <!-- Quick Suggestions -->
                        <div id="excludeSuggestionsSettings" class="mt-3">
                            <div class="text-xs text-gray-500 dark:text-gray-400 mb-2">Quick suggestions:</div>
                            <div class="flex flex-wrap gap-1">
                                <button type="button" class="exclude-suggestion text-xs bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 px-2 py-1 rounded hover:bg-red-100 dark:hover:bg-red-900 hover:text-red-700 dark:hover:text-red-300" data-term="recruiter">recruiter</button>
                                <button type="button" class="exclude-suggestion text-xs bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 px-2 py-1 rounded hover:bg-red-100 dark:hover:bg-red-900 hover:text-red-700 dark:hover:text-red-300" data-term="student">student</button>
                                <button type="button" class="exclude-suggestion text-xs bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 px-2 py-1 rounded hover:bg-red-100 dark:hover:bg-red-900 hover:text-red-700 dark:hover:text-red-300" data-term="intern">intern</button>
                                <button type="button" class="exclude-suggestion text-xs bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 px-2 py-1 rounded hover:bg-red-100 dark:hover:bg-red-900 hover:text-red-700 dark:hover:text-red-300" data-term="assistant">assistant</button>
                            </div>
                        </div>
                    </div>

                    <!-- Facebook Content Filters -->
                    <div class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-600">
                        <h4 class="font-semibold mb-3 text-gray-800 dark:text-gray-200">
                            <i class="fas fa-filter mr-2 text-purple-600"></i>Content Filters
                        </h4>

                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Content Type (for Posts/Content searches)</label>
                                <div class="grid grid-cols-2 gap-2">
                                    <label class="flex items-center p-2 border border-gray-200 dark:border-gray-600 rounded cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700">
                                        <input type="checkbox" id="includePhotos" class="mr-2 text-blue-600" checked>
                                        <span class="text-sm">Photos</span>
                                    </label>
                                    <label class="flex items-center p-2 border border-gray-200 dark:border-gray-600 rounded cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700">
                                        <input type="checkbox" id="includeVideos" class="mr-2 text-blue-600" checked>
                                        <span class="text-sm">Videos</span>
                                    </label>
                                    <label class="flex items-center p-2 border border-gray-200 dark:border-gray-600 rounded cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700">
                                        <input type="checkbox" id="includeLinks" class="mr-2 text-blue-600" checked>
                                        <span class="text-sm">Links</span>
                                    </label>
                                    <label class="flex items-center p-2 border border-gray-200 dark:border-gray-600 rounded cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700">
                                        <input type="checkbox" id="includeCheckins" class="mr-2 text-blue-600">
                                        <span class="text-sm">Check-ins</span>
                                    </label>
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Time Filter</label>
                                <select id="timeFilter" class="w-full p-2 border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 text-sm">
                                    <option value="">Any time</option>
                                    <option value="day">Past 24 hours</option>
                                    <option value="week">Past week</option>
                                    <option value="month">Past month</option>
                                    <option value="year">Past year</option>
                                </select>
                            </div>
                        </div>
                    </div>



                    <!-- Search Templates -->
                    <div class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-600">
                        <div class="flex items-center justify-between mb-3">
                            <h4 class="font-semibold text-gray-800 dark:text-gray-200">
                                <i class="fas fa-bookmark mr-2 text-green-600"></i>Search Templates & Presets
                            </h4>
                            <div class="flex items-center space-x-2">
                                <button type="button" id="saveCurrentAsTemplate" class="text-xs bg-green-600 text-white px-2 py-1 rounded hover:bg-green-700">
                                    <i class="fas fa-plus mr-1"></i>Save Current
                                </button>
                                <button type="button" id="manageTemplatesSettings" class="text-xs text-blue-600 hover:text-blue-800">
                                    <i class="fas fa-cog mr-1"></i>Manage
                                </button>
                            </div>
                        </div>

                        <div id="templatesListSettings" class="space-y-2 max-h-32 overflow-y-auto">
                            <div class="text-xs text-gray-500 dark:text-gray-400">No templates saved yet</div>
                        </div>

                        <!-- Quick Actions -->
                        <div class="mt-3 pt-3 border-t border-gray-200 dark:border-gray-600">
                            <div class="flex justify-between items-center">
                                <button type="button" id="clearAllTemplates" class="text-xs text-red-600 hover:text-red-800">
                                    <i class="fas fa-trash mr-1"></i>Clear All Templates
                                </button>
                                <button type="button" id="exportTemplates" class="text-xs text-blue-600 hover:text-blue-800">
                                    <i class="fas fa-download mr-1"></i>Export Templates
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Settings Actions -->
                    <div class="flex justify-between items-center pt-4 border-t border-gray-200 dark:border-gray-600">
                        <button type="button" id="resetAllSettings" class="text-sm text-red-600 hover:text-red-800">
                            <i class="fas fa-undo mr-1"></i>Reset All Settings
                        </button>
                        <button type="button" id="saveSettingsPreset" class="text-sm bg-blue-600 text-white px-3 py-1 rounded hover:bg-blue-700">
                            <i class="fas fa-save mr-1"></i>Save as Preset
                        </button>
                    </div>
                </div>
            </div>
        </div>
        </div>

        <!-- Modern Navigation Tabs -->
        <div class="container mx-auto max-w-5xl">
            <div class="mb-8 mt-12">
                <div class="flex space-x-0 bg-gray-100 dark:bg-gray-700 rounded-lg p-1 shadow-lg border border-gray-200 dark:border-gray-600">
                    <button id="basicSearchTab" class="linkedin-tab active flex-1 flex items-center justify-center px-6 py-3 rounded-lg font-medium transition-all duration-200">
                        <i class="fas fa-search mr-2"></i>Basic Search
                    </button>
                    <button id="bulkSearchTab" class="linkedin-tab flex-1 flex items-center justify-center px-6 py-3 rounded-lg font-medium transition-all duration-200">
                        <i class="fas fa-layer-group mr-2"></i>Bulk Search
                    </button>
                    <button id="visualBuilderTab" class="linkedin-tab flex-1 flex items-center justify-center px-6 py-3 rounded-lg font-medium transition-all duration-200">
                        <i class="fas fa-puzzle-piece mr-2"></i>Visual Query Builder
                    </button>
                </div>
            </div>

            <!-- Favorite Searches Section -->
            <div id="favoriteSearchesSection" class="container mx-auto max-w-5xl mb-6 hidden">
                <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6 shadow-sm">
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center">
                            <i class="fas fa-star text-yellow-500 mr-3"></i>
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">Favorite Searches</h3>
                        </div>
                        <button id="manageFavorites" class="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">
                            <i class="fas fa-cog mr-1"></i>Manage
                        </button>
                    </div>

                    <div id="favoriteSearchesList" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <!-- Default favorite searches -->
                        <div class="favorite-search-card bg-gray-50 dark:bg-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600 hover:shadow-md transition-shadow cursor-pointer">
                            <div class="flex items-center justify-between mb-2">
                                <h4 class="font-medium text-gray-900 dark:text-white">Marketing Managers</h4>
                                <i class="fas fa-star text-yellow-500"></i>
                            </div>
                            <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">Find marketing professionals in tech companies</p>
                            <div class="flex items-center justify-between">
                                <span class="text-xs text-gray-500 dark:text-gray-400">Used 12 times</span>
                                <button class="text-xs text-blue-600 hover:text-blue-800 dark:text-blue-400">Use Search</button>
                            </div>
                        </div>

                        <div class="favorite-search-card bg-gray-50 dark:bg-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600 hover:shadow-md transition-shadow cursor-pointer">
                            <div class="flex items-center justify-between mb-2">
                                <h4 class="font-medium text-gray-900 dark:text-white">Software Engineers</h4>
                                <i class="fas fa-star text-yellow-500"></i>
                            </div>
                            <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">Find developers and engineers</p>
                            <div class="flex items-center justify-between">
                                <span class="text-xs text-gray-500 dark:text-gray-400">Used 8 times</span>
                                <button class="text-xs text-blue-600 hover:text-blue-800 dark:text-blue-400">Use Search</button>
                            </div>
                        </div>

                        <div class="favorite-search-card bg-gray-50 dark:bg-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600 hover:shadow-md transition-shadow cursor-pointer">
                            <div class="flex items-center justify-between mb-2">
                                <h4 class="font-medium text-gray-900 dark:text-white">Sales Directors</h4>
                                <i class="fas fa-star text-yellow-500"></i>
                            </div>
                            <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">Find sales leadership and VPs</p>
                            <div class="flex items-center justify-between">
                                <span class="text-xs text-gray-500 dark:text-gray-400">Used 15 times</span>
                                <button class="text-xs text-blue-600 hover:text-blue-800 dark:text-blue-400">Use Search</button>
                            </div>
                        </div>
                    </div>

                    <div class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-600">
                        <button id="addToFavorites" class="text-sm text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-300">
                            <i class="fas fa-plus mr-1"></i>Add Current Search to Favorites
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Search Templates -->
        <div class="container mx-auto max-w-5xl">
            <div class="mb-6">
                <button id="toggleTemplates" class="text-facebook-blue hover:text-facebook-darkBlue font-medium">
                    <i class="fas fa-template mr-2"></i>Quick Templates <i class="fas fa-chevron-down ml-1"></i>
                </button>
                <div id="templatesContainer" class="hidden mt-4 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                    <!-- Templates will be populated by JavaScript -->
                </div>
            </div>
        </div>


        <!-- Search History & Saved Searches -->
        <div id="searchHistorySection" class="container mx-auto max-w-5xl">
            <div class="mb-6">
            <button id="toggleSearchHistory" class="flex items-center space-x-2 text-facebook-blue hover:text-facebook-darkBlue transition-colors duration-200">
                <i class="fas fa-chevron-down transition-transform duration-200" id="historyChevron"></i>
                <span class="font-medium">Search History & Saved Searches</span>
            </button>
            <div id="searchHistoryContainer" class="hidden mt-4">
                <div class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
                    <div class="flex justify-end space-x-2 mb-4">
                        <button id="saveCurrentSearch" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg text-sm transition-colors duration-200">
                            <i class="fas fa-save mr-2"></i>Save Current
                        </button>
                        <button id="clearHistory" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg text-sm transition-colors duration-200">
                            <i class="fas fa-trash mr-2"></i>Clear History
                        </button>
                    </div>

                    <!-- History Tabs -->
                    <div class="flex space-x-1 mb-4 bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
                        <button class="history-tab active flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors duration-200 border-b-2 border-facebook-blue text-facebook-blue">
                            Recent Searches
                        </button>
                        <button class="history-tab flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors duration-200 border-b-2 border-transparent text-gray-500 hover:text-gray-700">
                            Saved Searches
                        </button>
                        <button class="history-tab flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors duration-200 border-b-2 border-transparent text-gray-500 hover:text-gray-700">
                            Favorites
                        </button>
                    </div>

                    <!-- History Content -->
                    <div class="history-tab-content">
                        <!-- Recent Searches List -->
                        <div id="recentSearchesList" class="space-y-2">
                            <div class="text-center text-gray-500 dark:text-gray-400 py-4">No recent searches</div>
                        </div>

                        <!-- Saved Searches List -->
                        <div id="savedSearchesList" class="space-y-2 hidden">
                            <div class="text-center text-gray-500 dark:text-gray-400 py-4">No saved searches</div>
                        </div>

                        <!-- Favorites List -->
                        <div id="favoritesList" class="space-y-2 hidden">
                            <div class="text-center text-gray-500 dark:text-gray-400 py-4">No favorites</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        </div>

    <!-- Tab Content Containers -->
    <div class="container mx-auto max-w-5xl">
        <div id="tabContent">
            <!-- Modern Basic Search Tab -->
            <div id="basicSearchContent" class="tab-content">
                <!-- LinkedIn-style Basic Search Card -->
                <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-gray-200 dark:border-gray-700 p-8 mb-6">
                    <div class="flex items-center mb-6">
                        <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center mr-4 shadow-lg">
                            <i class="fab fa-facebook-f text-white text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-xl font-semibold text-gray-900 dark:text-white">Facebook People Search</h3>
                            <p class="text-gray-600 dark:text-gray-400 mt-1">Find friends, family, and connections on Facebook</p>
                        </div>
                    </div>

                    <form id="searchForm" class="space-y-8">
                        <!-- Search Type Selection -->
                        <div class="beautiful-input-group">
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                    <i class="fas fa-search text-blue-500 text-lg"></i>
                                </div>
                                <select id="searchType" class="beautiful-select pl-12 pr-4 py-4 w-full bg-white dark:bg-gray-800 border-2 border-gray-200 dark:border-gray-600 rounded-xl focus:border-blue-500 focus:ring-4 focus:ring-blue-100 dark:focus:ring-blue-900 transition-all duration-300 text-gray-900 dark:text-gray-100 font-medium shadow-sm hover:shadow-md">
                                    <option value="">Select Search Type</option>
                                    <option value="people">👤 People</option>
                                    <option value="pages">📄 Pages</option>
                                    <option value="groups">👥 Groups</option>
                                    <option value="posts">📝 Posts</option>
                                    <option value="events">📅 Events</option>
                                    <option value="marketplace">🛒 Marketplace</option>
                                </select>
                                <div class="absolute inset-y-0 right-0 pr-4 flex items-center pointer-events-none">
                                    <i class="fas fa-chevron-down text-gray-400"></i>
                                </div>
                            </div>
                            <label class="beautiful-label">Search Type</label>
                            <p class="beautiful-hint">Choose what you want to search for on Facebook</p>
                            <div id="searchType-error" class="error-message hidden"></div>
                        </div>

                        <!-- Beautiful Form Fields -->
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                            <!-- Field Input -->
                            <div class="beautiful-input-group">
                                <div class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                        <i class="fas fa-briefcase text-purple-500 text-lg"></i>
                                    </div>
                                    <input type="text" id="field" class="beautiful-input pl-12 pr-4 py-4 w-full bg-white dark:bg-gray-800 border-2 border-gray-200 dark:border-gray-600 rounded-xl focus:border-purple-500 focus:ring-4 focus:ring-purple-100 dark:focus:ring-purple-900 transition-all duration-300 text-gray-900 dark:text-gray-100 font-medium shadow-sm hover:shadow-md placeholder-transparent" placeholder="Field" autocomplete="off">
                                    <label for="field" class="beautiful-floating-label">Field</label>
                                </div>
                                <p class="beautiful-hint">e.g. Photography, Travel, Music, Sports</p>
                                <div id="fieldSuggestions" class="autocomplete-suggestions hidden"></div>
                                <div id="field-error" class="error-message hidden"></div>
                                <div id="field-success" class="success-message hidden"></div>
                            </div>

                            <!-- Name Input -->
                            <div class="beautiful-input-group">
                                <div class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                        <i class="fas fa-user text-green-500 text-lg"></i>
                                    </div>
                                    <input type="text" id="name" class="beautiful-input pl-12 pr-4 py-4 w-full bg-white dark:bg-gray-800 border-2 border-gray-200 dark:border-gray-600 rounded-xl focus:border-green-500 focus:ring-4 focus:ring-green-100 dark:focus:ring-green-900 transition-all duration-300 text-gray-900 dark:text-gray-100 font-medium shadow-sm hover:shadow-md placeholder-transparent" placeholder="Name" autocomplete="off">
                                    <label for="name" class="beautiful-floating-label">Name</label>
                                </div>
                                <div class="name-validation flex items-center mt-2 hidden">
                                    <i class="fas fa-check-circle text-green-500 mr-2"></i>
                                    <span class="text-green-600 text-sm font-medium">✓ Valid name format</span>
                                </div>
                                <p class="beautiful-hint">e.g. John Doe, Sarah Smith</p>
                                <div id="nameSuggestions" class="autocomplete-suggestions hidden"></div>
                                <div id="name-error" class="error-message hidden"></div>
                                <div id="name-success" class="success-message hidden"></div>
                            </div>
                        </div>
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mt-8">
                            <!-- About/Interests Input -->
                            <div class="beautiful-input-group">
                                <div class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                        <i class="fas fa-heart text-pink-500 text-lg"></i>
                                    </div>
                                    <input type="text" id="jobTitle" class="beautiful-input pl-12 pr-4 py-4 w-full bg-white dark:bg-gray-800 border-2 border-gray-200 dark:border-gray-600 rounded-xl focus:border-pink-500 focus:ring-4 focus:ring-pink-100 dark:focus:ring-pink-900 transition-all duration-300 text-gray-900 dark:text-gray-100 font-medium shadow-sm hover:shadow-md placeholder-transparent" placeholder="About/Interests" autocomplete="off">
                                    <label for="jobTitle" class="beautiful-floating-label">About/Interests</label>
                                </div>
                                <p class="beautiful-hint">e.g. Love photography, Travel enthusiast, Music lover</p>
                                <div id="jobTitleSuggestions" class="autocomplete-suggestions hidden"></div>
                                <div id="jobTitle-error" class="error-message hidden"></div>
                                <div id="jobTitle-success" class="success-message hidden"></div>
                            </div>

                            <!-- Email Provider Select -->
                            <div class="beautiful-input-group">
                                <div class="relative">
                                    <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                        <i class="fas fa-envelope text-orange-500 text-lg"></i>
                                    </div>
                                    <select id="emailProvider" class="beautiful-select pl-12 pr-4 py-4 w-full bg-white dark:bg-gray-800 border-2 border-gray-200 dark:border-gray-600 rounded-xl focus:border-orange-500 focus:ring-4 focus:ring-orange-100 dark:focus:ring-orange-900 transition-all duration-300 text-gray-900 dark:text-gray-100 font-medium shadow-sm hover:shadow-md">
                                        <option value="">Select Email Provider</option>
                                        <option value="gmail.com">📧 Gmail</option>
                                        <option value="outlook.com">📧 Outlook/Hotmail</option>
                                        <option value="yahoo.com">📧 Yahoo</option>
                                        <option value="aol.com">📧 AOL</option>
                                        <option value="icloud.com">📧 iCloud</option>
                                        <option value="protonmail.com">🔒 ProtonMail</option>
                                        <option value="company.com">🏢 Company Email</option>
                                        <option value="other">❓ Other</option>
                                    </select>
                                    <div class="absolute inset-y-0 right-0 pr-4 flex items-center pointer-events-none">
                                        <i class="fas fa-chevron-down text-gray-400"></i>
                                    </div>
                                </div>
                                <label class="beautiful-label">Email Provider</label>
                                <div id="emailProvider-error" class="error-message hidden"></div>
                            </div>
                        </div>

                        <!-- Location Fields -->
                        <div class="mt-8">
                            <div class="flex items-center mb-6">
                                <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-map-marker-alt text-white"></i>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-800 dark:text-gray-200">Location Targeting</h4>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">Narrow down your search by geographic location</p>
                                </div>
                            </div>
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                <div class="beautiful-input-group">
                                    <div class="relative">
                                        <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                            <i class="fas fa-globe text-blue-500 text-lg"></i>
                                        </div>
                                        <input type="text" id="country" class="beautiful-input pl-12 pr-4 py-4 w-full bg-white dark:bg-gray-800 border-2 border-gray-200 dark:border-gray-600 rounded-xl focus:border-blue-500 focus:ring-4 focus:ring-blue-100 dark:focus:ring-blue-900 transition-all duration-300 text-gray-900 dark:text-gray-100 font-medium shadow-sm hover:shadow-md placeholder-transparent" placeholder="Country" autocomplete="off">
                                        <label for="country" class="beautiful-floating-label">Country</label>
                                    </div>
                                    <p class="beautiful-hint">e.g. United States, Canada</p>
                                    <div id="countrySuggestions" class="autocomplete-suggestions hidden"></div>
                                    <div id="country-error" class="error-message hidden"></div>
                                </div>
                                <div class="beautiful-input-group">
                                    <div class="relative">
                                        <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                            <i class="fas fa-map text-purple-500 text-lg"></i>
                                        </div>
                                        <input type="text" id="state" class="beautiful-input pl-12 pr-4 py-4 w-full bg-white dark:bg-gray-800 border-2 border-gray-200 dark:border-gray-600 rounded-xl focus:border-purple-500 focus:ring-4 focus:ring-purple-100 dark:focus:ring-purple-900 transition-all duration-300 text-gray-900 dark:text-gray-100 font-medium shadow-sm hover:shadow-md placeholder-transparent" placeholder="State" autocomplete="off">
                                        <label for="state" class="beautiful-floating-label">State</label>
                                    </div>
                                    <p class="beautiful-hint">e.g. California, New York</p>
                                    <div id="stateSuggestions" class="autocomplete-suggestions hidden"></div>
                                    <div id="state-error" class="error-message hidden"></div>
                                </div>
                                <div class="beautiful-input-group">
                                    <div class="relative">
                                        <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                                            <i class="fas fa-city text-green-500 text-lg"></i>
                                        </div>
                                        <input type="text" id="city" class="beautiful-input pl-12 pr-4 py-4 w-full bg-white dark:bg-gray-800 border-2 border-gray-200 dark:border-gray-600 rounded-xl focus:border-green-500 focus:ring-4 focus:ring-green-100 dark:focus:ring-green-900 transition-all duration-300 text-gray-900 dark:text-gray-100 font-medium shadow-sm hover:shadow-md placeholder-transparent" placeholder="City" autocomplete="off">
                                        <label for="city" class="beautiful-floating-label">City</label>
                                    </div>
                                    <p class="beautiful-hint">e.g. San Francisco, Boston</p>
                                    <div id="citySuggestions" class="autocomplete-suggestions hidden"></div>
                                    <div id="city-error" class="error-message hidden"></div>
                                </div>
                            </div>
                        </div>

                        <!-- AI-Powered Search Suggestions -->
                        <div class="mt-6">
                            <div class="bg-gradient-to-r from-purple-50 to-blue-50 dark:from-gray-800 dark:to-gray-700 rounded-lg border border-purple-200 dark:border-gray-600 p-6">
                                <!-- Header -->
                                <div class="flex items-center justify-between mb-6">
                                    <div class="flex items-center">
                                        <div class="bg-purple-100 dark:bg-purple-900 p-2 rounded-lg mr-3">
                                            <i class="fas fa-magic text-purple-600 dark:text-purple-400"></i>
                                        </div>
                                        <h4 class="text-lg font-semibold text-gray-800 dark:text-gray-200">AI-Powered Search Suggestions</h4>
                                    </div>
                                    <button id="refreshSuggestions" class="text-purple-600 hover:text-purple-800 dark:text-purple-400 dark:hover:text-purple-300 text-sm font-medium">
                                        <i class="fas fa-refresh mr-1"></i>Refresh
                                    </button>
                                </div>

                                <!-- Related Fields -->
                                <div id="relatedFieldsSection" class="mb-6 hidden">
                                    <div class="flex items-center mb-3">
                                        <i class="fas fa-sitemap text-blue-500 mr-2"></i>
                                        <h5 class="font-medium text-gray-800 dark:text-gray-200">Related Fields</h5>
                                    </div>
                                    <div id="relatedFields" class="flex flex-wrap gap-2">
                                        <!-- Dynamic content will be populated here -->
                                    </div>
                                </div>

                                <!-- Similar Interests -->
                                <div id="similarJobTitlesSection" class="mb-6 hidden">
                                    <div class="flex items-center mb-3">
                                        <i class="fas fa-heart text-pink-500 mr-2"></i>
                                        <h5 class="font-medium text-gray-800 dark:text-gray-200">Similar Interests</h5>
                                    </div>
                                    <div id="similarJobTitles" class="flex flex-wrap gap-2">
                                        <!-- Dynamic content will be populated here -->
                                    </div>
                                </div>

                                <!-- Trending Interests -->
                                <div>
                                    <div class="flex items-center mb-3">
                                        <i class="fas fa-fire text-orange-500 mr-2"></i>
                                        <h5 class="font-medium text-gray-800 dark:text-gray-200">Trending Interests</h5>
                                    </div>
                                    <div id="trendingSearches" class="flex flex-wrap gap-2">
                                        <span class="bg-orange-100 hover:bg-orange-200 dark:bg-orange-900 dark:hover:bg-orange-800 text-orange-800 dark:text-orange-200 px-3 py-1 rounded-full text-sm cursor-pointer transition-colors duration-200">Photography</span>
                                        <span class="bg-orange-100 hover:bg-orange-200 dark:bg-orange-900 dark:hover:bg-orange-800 text-orange-800 dark:text-orange-200 px-3 py-1 rounded-full text-sm cursor-pointer transition-colors duration-200">Travel</span>
                                        <span class="bg-orange-100 hover:bg-orange-200 dark:bg-orange-900 dark:hover:bg-orange-800 text-orange-800 dark:text-orange-200 px-3 py-1 rounded-full text-sm cursor-pointer transition-colors duration-200">Music</span>
                                        <span class="bg-orange-100 hover:bg-orange-200 dark:bg-orange-900 dark:hover:bg-orange-800 text-orange-800 dark:text-orange-200 px-3 py-1 rounded-full text-sm cursor-pointer transition-colors duration-200">Fitness</span>
                                        <span class="bg-orange-100 hover:bg-orange-200 dark:bg-orange-900 dark:hover:bg-orange-800 text-orange-800 dark:text-orange-200 px-3 py-1 rounded-full text-sm cursor-pointer transition-colors duration-200">Cooking</span>
                                        <span class="bg-orange-100 hover:bg-orange-200 dark:bg-orange-900 dark:hover:bg-orange-800 text-orange-800 dark:text-orange-200 px-3 py-1 rounded-full text-sm cursor-pointer transition-colors duration-200">Gaming</span>
                                        <span class="bg-orange-100 hover:bg-orange-200 dark:bg-orange-900 dark:hover:bg-orange-800 text-orange-800 dark:text-orange-200 px-3 py-1 rounded-full text-sm cursor-pointer transition-colors duration-200">Art & Design</span>
                                        <span class="bg-orange-100 hover:bg-orange-200 dark:bg-orange-900 dark:hover:bg-orange-800 text-orange-800 dark:text-orange-200 px-3 py-1 rounded-full text-sm cursor-pointer transition-colors duration-200">Sports</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Profile Enhancement Preview -->
                        <div class="mt-6">
                            <div class="bg-gradient-to-r from-purple-50 to-pink-50 dark:from-gray-800 dark:to-gray-700 rounded-lg p-6 border border-purple-200 dark:border-gray-600">
                                <div class="flex items-center justify-between mb-4">
                                    <h4 class="text-lg font-semibold text-gray-800 dark:text-gray-200 flex items-center">
                                        <i class="fas fa-user-friends mr-3 text-purple-600"></i>Profile Enhancement Preview
                                    </h4>
                                    <div class="flex items-center gap-4">
                                        <div class="flex items-center">
                                            <span id="enrichmentStatus" class="text-sm text-gray-600 dark:text-gray-400 mr-2">Enabled</span>
                                            <label class="relative inline-flex items-center cursor-pointer">
                                                <input type="checkbox" id="enrichmentToggle" class="sr-only peer" checked>
                                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 dark:peer-focus:ring-purple-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-purple-600"></div>
                                            </label>
                                        </div>
                                        <button type="button" id="enrichmentSettings" class="text-purple-600 hover:text-purple-800 text-sm font-medium">
                                            <i class="fas fa-cog mr-1"></i>Settings
                                            <i class="fas fa-chevron-down ml-1"></i>
                                        </button>
                                    </div>
                                </div>

                                <div class="text-sm text-gray-600 dark:text-gray-400 mb-6">
                                    Automatically enhance found Facebook profiles with additional information including mutual friends, shared interests, and social connections.
                                </div>

                                <div id="enrichmentPreview" class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <!-- Mutual Friends -->
                                    <div id="emailPatternsPreview" class="bg-white dark:bg-gray-800 rounded-lg p-3 border border-gray-200 dark:border-gray-600 shadow-sm">
                                        <div class="flex items-center mb-2">
                                            <i class="fas fa-user-friends text-blue-600 mr-2"></i>
                                            <h5 class="font-medium text-sm text-gray-700 dark:text-gray-300">Mutual Friends</h5>
                                        </div>
                                        <div class="space-y-1 text-xs text-gray-500 dark:text-gray-400">
                                            <div class="flex items-center bg-gray-50 dark:bg-gray-700 p-1.5 rounded">
                                                <i class="fas fa-user-circle text-blue-500 mr-2"></i>
                                                <span>Sarah Johnson (12 mutual)</span>
                                            </div>
                                            <div class="flex items-center bg-gray-50 dark:bg-gray-700 p-1.5 rounded">
                                                <i class="fas fa-user-circle text-green-500 mr-2"></i>
                                                <span>Mike Chen (8 mutual)</span>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Shared Interests -->
                                    <div id="socialMediaPreview" class="bg-white dark:bg-gray-800 rounded-lg p-3 border border-gray-200 dark:border-gray-600 shadow-sm">
                                        <div class="flex items-center mb-2">
                                            <i class="fas fa-heart text-pink-600 mr-2"></i>
                                            <h5 class="font-medium text-sm text-gray-700 dark:text-gray-300">Shared Interests</h5>
                                        </div>
                                        <div class="space-y-1 text-xs text-gray-500 dark:text-gray-400">
                                            <div class="flex items-center bg-gray-50 dark:bg-gray-700 p-1.5 rounded">
                                                <i class="fas fa-camera text-purple-600 mr-2"></i>
                                                <span>Photography</span>
                                            </div>
                                            <div class="flex items-center bg-gray-50 dark:bg-gray-700 p-1.5 rounded">
                                                <i class="fas fa-plane text-blue-600 mr-2"></i>
                                                <span>Travel</span>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Social Activity -->
                                    <div id="companyDataPreview" class="bg-white dark:bg-gray-800 rounded-lg p-3 border border-gray-200 dark:border-gray-600 shadow-sm md:col-span-2">
                                        <div class="flex items-center mb-2">
                                            <i class="fas fa-chart-line text-green-600 mr-2"></i>
                                            <h5 class="font-medium text-sm text-gray-700 dark:text-gray-300">Social Activity</h5>
                                        </div>
                                        <div class="grid grid-cols-1 md:grid-cols-3 gap-2 text-xs text-gray-500 dark:text-gray-400">
                                            <div class="bg-gray-50 dark:bg-gray-700 p-1.5 rounded">
                                                <span class="font-medium">Last Active:</span> 2 hours ago
                                            </div>
                                            <div class="bg-gray-50 dark:bg-gray-700 p-1.5 rounded">
                                                <span class="font-medium">Posts/Month:</span> 15-20 posts
                                            </div>
                                            <div class="bg-gray-50 dark:bg-gray-700 p-1.5 rounded">
                                                <span class="font-medium">Friends:</span> 450+ connections
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Expandable Enrichment Settings -->
                            <div id="enrichmentSettingsContainer" class="hidden mt-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
                                <div class="space-y-6">
                                    <!-- Data Points to Enrich -->
                                    <div class="space-y-4">
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center">
                                                <i class="fas fa-envelope text-blue-600 mr-2"></i>
                                                <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Email Patterns</span>
                                            </div>
                                            <label class="relative inline-flex items-center cursor-pointer">
                                                <input type="checkbox" class="sr-only peer" checked>
                                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-orange-300 dark:peer-focus:ring-orange-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-orange-500"></div>
                                            </label>
                                        </div>
                                    </div>

                                    <!-- Social Media Platforms -->
                                    <div class="space-y-4">
                                        <div class="flex items-center">
                                            <i class="fas fa-share-alt text-green-600 mr-2"></i>
                                            <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300">Social Media Platforms</h4>
                                        </div>
                                        <div class="space-y-3 ml-6">
                                            <div class="flex items-center justify-between">
                                                <div class="flex items-center">
                                                    <i class="fab fa-twitter text-blue-400 mr-2"></i>
                                                    <span class="text-sm text-gray-600 dark:text-gray-400">Twitter</span>
                                                </div>
                                                <label class="relative inline-flex items-center cursor-pointer">
                                                    <input type="checkbox" class="sr-only peer" checked>
                                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-orange-300 dark:peer-focus:ring-orange-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-orange-500"></div>
                                                </label>
                                            </div>
                                            <div class="flex items-center justify-between">
                                                <div class="flex items-center">
                                                    <i class="fab fa-facebook text-blue-600 mr-2"></i>
                                                    <span class="text-sm text-gray-600 dark:text-gray-400">Facebook</span>
                                                </div>
                                                <label class="relative inline-flex items-center cursor-pointer">
                                                    <input type="checkbox" class="sr-only peer" checked>
                                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-orange-300 dark:peer-focus:ring-orange-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-orange-500"></div>
                                                </label>
                                            </div>
                                            <div class="flex items-center justify-between">
                                                <div class="flex items-center">
                                                    <i class="fab fa-instagram text-pink-500 mr-2"></i>
                                                    <span class="text-sm text-gray-600 dark:text-gray-400">Instagram</span>
                                                </div>
                                                <label class="relative inline-flex items-center cursor-pointer">
                                                    <input type="checkbox" class="sr-only peer" checked>
                                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-orange-300 dark:peer-focus:ring-orange-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after-border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-orange-500"></div>
                                                </label>
                                            </div>
                                            <div class="flex items-center justify-between">
                                                <div class="flex items-center">
                                                    <i class="fab fa-linkedin text-blue-700 mr-2"></i>
                                                    <span class="text-sm text-gray-600 dark:text-gray-400">LinkedIn Secondary</span>
                                                </div>
                                                <label class="relative inline-flex items-center cursor-pointer">
                                                    <input type="checkbox" class="sr-only peer" checked>
                                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-orange-300 dark:peer-focus:ring-orange-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-orange-500"></div>
                                                </label>
                                            </div>
                                            <div class="flex items-center justify-between">
                                                <div class="flex items-center">
                                                    <i class="fab fa-tiktok text-black mr-2"></i>
                                                    <span class="text-sm text-gray-600 dark:text-gray-400">TikTok</span>
                                                </div>
                                                <label class="relative inline-flex items-center cursor-pointer">
                                                    <input type="checkbox" class="sr-only peer" checked>
                                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-orange-300 dark:peer-focus:ring-orange-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-orange-500"></div>
                                                </label>
                                            </div>
                                            <div class="flex items-center justify-between">
                                                <div class="flex items-center">
                                                    <i class="fab fa-youtube text-red-600 mr-2"></i>
                                                    <span class="text-sm text-gray-600 dark:text-gray-400">YouTube</span>
                                                </div>
                                                <label class="relative inline-flex items-center cursor-pointer">
                                                    <input type="checkbox" class="sr-only peer" checked>
                                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-orange-300 dark:peer-focus:ring-orange-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-orange-500"></div>
                                                </label>
                                            </div>
                                            <div class="flex items-center justify-between">
                                                <div class="flex items-center">
                                                    <i class="fab fa-discord text-purple-600 mr-2"></i>
                                                    <span class="text-sm text-gray-600 dark:text-gray-400">Discord</span>
                                                </div>
                                                <label class="relative inline-flex items-center cursor-pointer">
                                                    <input type="checkbox" class="sr-only peer" checked>
                                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-orange-300 dark:peer-focus:ring-orange-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-orange-500"></div>
                                                </label>
                                            </div>
                                            <div class="flex items-center justify-between">
                                                <div class="flex items-center">
                                                    <i class="fab fa-github text-gray-800 mr-2"></i>
                                                    <span class="text-sm text-gray-600 dark:text-gray-400">GitHub</span>
                                                </div>
                                                <label class="relative inline-flex items-center cursor-pointer">
                                                    <input type="checkbox" class="sr-only peer" checked>
                                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-orange-300 dark:peer-focus:ring-orange-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-orange-500"></div>
                                                </label>
                                            </div>
                                            <div class="flex items-center justify-between">
                                                <div class="flex items-center">
                                                    <i class="fas fa-globe text-green-600 mr-2"></i>
                                                    <span class="text-sm text-gray-600 dark:text-gray-400">Personal Website</span>
                                                </div>
                                                <label class="relative inline-flex items-center cursor-pointer">
                                                    <input type="checkbox" class="sr-only peer" checked>
                                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-orange-300 dark:peer-focus:ring-orange-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-orange-500"></div>
                                                </label>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Company Data -->
                                    <div class="space-y-4">
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center">
                                                <i class="fas fa-building text-purple-600 mr-2"></i>
                                                <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Company Data</span>
                                            </div>
                                            <label class="relative inline-flex items-center cursor-pointer">
                                                <input type="checkbox" class="sr-only peer" checked>
                                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-orange-300 dark:peer-focus:ring-orange-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-orange-500"></div>
                                            </label>
                                        </div>
                                    </div>

                                    <!-- API Preferences -->
                                    <div class="space-y-4">
                                        <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300">API Preferences</h4>
                                        <div class="space-y-3">
                                            <div>
                                                <label class="block text-sm text-gray-600 dark:text-gray-400 mb-2">Primary Enrichment Provider</label>
                                                <select class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-700 dark:text-white focus:ring-2 focus:ring-orange-500 focus:border-orange-500">
                                                    <option>Clearbit (Recommended)</option>
                                                    <option>Hunter.io</option>
                                                    <option>Apollo</option>
                                                    <option>Facebook Graph API</option>
                                                </select>
                                            </div>
                                            <div>
                                                <label class="block text-sm text-gray-600 dark:text-gray-400 mb-2">Fallback Provider</label>
                                                <select class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-700 dark:text-white focus:ring-2 focus:ring-orange-500 focus:border-orange-500">
                                                    <option>Hunter.io</option>
                                                    <option>Apollo</option>
                                                    <option>Facebook Graph API</option>
                                                    <option>None</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Data Quality Thresholds -->
                                    <div class="space-y-4">
                                        <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300">Data Quality Thresholds</h4>
                                        <div class="space-y-4">
                                            <div>
                                                <div class="flex justify-between items-center mb-2">
                                                    <label class="text-sm text-gray-600 dark:text-gray-400">Minimum Confidence Score</label>
                                                    <span class="text-sm font-medium text-gray-900 dark:text-white">75%</span>
                                                </div>
                                                <input type="range" min="0" max="100" value="75" class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700 slider-orange">
                                            </div>
                                            <div class="flex items-center justify-between">
                                                <span class="text-sm text-gray-600 dark:text-gray-400">Skip low-quality data</span>
                                                <label class="relative inline-flex items-center cursor-pointer">
                                                    <input type="checkbox" class="sr-only peer" checked>
                                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-orange-300 dark:peer-focus:ring-orange-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-orange-500"></div>
                                                </label>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Footer -->
                                    <div class="flex justify-between items-center mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
                                        <button type="button" onclick="resetEnrichmentSettings()" class="px-4 py-2 text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200">
                                            Reset to Defaults
                                        </button>
                                        <button type="button" onclick="saveEnrichmentSettings()" class="px-6 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700">
                                            Save Settings
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Facebook Advanced Search Options -->
                        <div class="mt-6">
                            <div class="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-gray-800 dark:to-gray-700 rounded-xl border-2 border-blue-200 dark:border-gray-600 shadow-lg">
                                <div class="p-6 border-b border-blue-200 dark:border-gray-600">
                                    <div class="flex items-center justify-between cursor-pointer" id="advancedOptionsToggle">
                                        <h4 class="font-semibold flex items-center text-gray-800 dark:text-gray-200">
                                            <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center mr-3">
                                                <i class="fas fa-sliders-h text-white"></i>
                                            </div>
                                            Facebook Advanced Search Options
                                        </h4>
                                        <i class="fas fa-chevron-down transition-transform duration-200 text-blue-600" id="advancedChevron"></i>
                                    </div>
                                </div>

                                <div id="advancedOptionsContent" class="hidden p-6 space-y-8">
                                    <!-- Facebook Advanced Filters -->
                                    <div>
                                        <h5 class="font-medium text-gray-800 dark:text-gray-200 mb-4 flex items-center">
                                            <i class="fab fa-facebook-f mr-2 text-blue-600"></i>
                                            Facebook Search Filters
                                        </h5>

                                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                            <!-- Page Category (for Facebook Pages) -->
                                            <div class="modern-input-group">
                                                <select id="pageCategory" class="modern-input">
                                                    <option value="">Any Category</option>
                                                    <option value="business">Business</option>
                                                    <option value="brand">Brand</option>
                                                    <option value="community">Community</option>
                                                    <option value="entertainment">Entertainment</option>
                                                    <option value="public-figure">Public Figure</option>
                                                    <option value="local-business">Local Business</option>
                                                    <option value="organization">Organization</option>
                                                    <option value="media">Media/News</option>
                                                </select>
                                                <label class="floating-label">Page Category</label>
                                            </div>

                                            <!-- Post Type -->
                                            <div class="modern-input-group">
                                                <select id="postType" class="modern-input">
                                                    <option value="">Any Post Type</option>
                                                    <option value="photo">Photos</option>
                                                    <option value="video">Videos</option>
                                                    <option value="link">Links</option>
                                                    <option value="status">Status Updates</option>
                                                    <option value="event">Events</option>
                                                    <option value="poll">Polls</option>
                                                    <option value="live">Live Videos</option>
                                                </select>
                                                <label class="floating-label">Post Type</label>
                                            </div>

                                            <!-- Group Type -->
                                            <div class="modern-input-group">
                                                <select id="groupType" class="modern-input">
                                                    <option value="">Any Group Type</option>
                                                    <option value="public">Public Groups</option>
                                                    <option value="closed">Closed Groups</option>
                                                    <option value="private">Private Groups</option>
                                                    <option value="buy-sell">Buy/Sell Groups</option>
                                                    <option value="professional">Professional Groups</option>
                                                    <option value="hobby">Hobby Groups</option>
                                                    <option value="local">Local Groups</option>
                                                </select>
                                                <label class="floating-label">Group Type</label>
                                            </div>

                                            <!-- Verification Status -->
                                            <div class="modern-input-group">
                                                <select id="verificationStatus" class="modern-input">
                                                    <option value="">Any Verification</option>
                                                    <option value="verified">Verified Only</option>
                                                    <option value="unverified">Unverified Only</option>
                                                    <option value="blue-check">Blue Checkmark</option>
                                                    <option value="gray-check">Gray Checkmark</option>
                                                </select>
                                                <label class="floating-label">Verification Status</label>
                                            </div>

                                            <!-- Content Language -->
                                            <div class="modern-input-group">
                                                <select id="contentLanguage" class="modern-input">
                                                    <option value="">Any Language</option>
                                                    <option value="en">English</option>
                                                    <option value="es">Spanish</option>
                                                    <option value="fr">French</option>
                                                    <option value="de">German</option>
                                                    <option value="it">Italian</option>
                                                    <option value="pt">Portuguese</option>
                                                    <option value="zh">Chinese</option>
                                                    <option value="ja">Japanese</option>
                                                    <option value="ar">Arabic</option>
                                                    <option value="hi">Hindi</option>
                                                </select>
                                                <label class="floating-label">Content Language</label>
                                            </div>

                                            <!-- Date Range -->
                                            <div class="modern-input-group">
                                                <select id="dateRange" class="modern-input">
                                                    <option value="">Any Time</option>
                                                    <option value="today">Today</option>
                                                    <option value="week">This Week</option>
                                                    <option value="month">This Month</option>
                                                    <option value="year">This Year</option>
                                                    <option value="custom">Custom Range</option>
                                                </select>
                                                <label class="floating-label">Date Range</label>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Facebook-Specific Checkboxes -->
                                    <div class="mt-6 space-y-3">
                                        <label class="flex items-center cursor-pointer">
                                            <input type="checkbox" id="verifiedProfiles" class="mr-3 rounded">
                                            <span class="text-sm text-gray-700 dark:text-gray-300">Verified profiles only</span>
                                        </label>

                                        <label class="flex items-center cursor-pointer">
                                            <input type="checkbox" id="publicProfiles" class="mr-3 rounded">
                                            <span class="text-sm text-gray-700 dark:text-gray-300">Public profiles only</span>
                                        </label>

                                        <label class="flex items-center cursor-pointer">
                                            <input type="checkbox" id="businessPages" class="mr-3 rounded">
                                            <span class="text-sm text-gray-700 dark:text-gray-300">Include business pages</span>
                                        </label>

                                        <label class="flex items-center cursor-pointer">
                                            <input type="checkbox" id="recentActivity" class="mr-3 rounded">
                                            <span class="text-sm text-gray-700 dark:text-gray-300">Recent activity only</span>
                                        </label>

                                        <label class="flex items-center cursor-pointer">
                                            <input type="checkbox" id="hasContactInfo" class="mr-3 rounded">
                                            <span class="text-sm text-gray-700 dark:text-gray-300">Has contact information</span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Search Preview & Validation -->
                        <div id="searchPreviewSection" class="mt-6 hidden">
                            <div class="bg-gradient-to-r from-blue-50 to-cyan-50 dark:from-gray-800 dark:to-gray-700 rounded-lg border border-blue-200 dark:border-gray-600 p-6">
                                <!-- Header -->
                                <div class="flex items-center justify-between mb-4">
                                    <h4 class="text-lg font-semibold text-gray-800 dark:text-gray-200 flex items-center">
                                        <i class="fas fa-eye mr-3 text-blue-600"></i>Search Preview & Validation
                                    </h4>
                                </div>

                                <!-- Search Statistics -->
                                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                                    <!-- Estimated Results -->
                                    <div class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-600 shadow-sm">
                                        <div class="flex items-center mb-2">
                                            <i class="fas fa-users text-blue-600 mr-2"></i>
                                            <h5 class="font-medium text-sm text-gray-700 dark:text-gray-300">Estimated Results</h5>
                                        </div>
                                        <div class="text-2xl font-bold text-blue-600 dark:text-blue-400" id="estimatedResults">150-500 profiles</div>
                                    </div>

                                    <!-- Search Difficulty -->
                                    <div class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-600 shadow-sm">
                                        <div class="flex items-center mb-2">
                                            <i class="fas fa-chart-line text-yellow-600 mr-2"></i>
                                            <h5 class="font-medium text-sm text-gray-700 dark:text-gray-300">Search Difficulty</h5>
                                        </div>
                                        <div class="text-2xl font-bold text-yellow-600 dark:text-yellow-400" id="searchDifficulty">Medium</div>
                                    </div>

                                    <!-- Success Rate -->
                                    <div class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-600 shadow-sm">
                                        <div class="flex items-center mb-2">
                                            <i class="fas fa-percentage text-green-600 mr-2"></i>
                                            <h5 class="font-medium text-sm text-gray-700 dark:text-gray-300">Success Rate</h5>
                                        </div>
                                        <div class="text-2xl font-bold text-green-600 dark:text-green-400" id="successRate">85%</div>
                                    </div>
                                </div>

                                <!-- Search Recommendations -->
                                <div class="space-y-3">
                                    <div class="flex items-start">
                                        <i class="fas fa-check-circle text-green-500 mr-2 mt-1"></i>
                                        <span class="text-sm text-gray-700 dark:text-gray-300">Good balance of specificity and reach</span>
                                    </div>
                                    <div class="flex items-start">
                                        <i class="fas fa-lightbulb text-yellow-500 mr-2 mt-1"></i>
                                        <span class="text-sm text-gray-700 dark:text-gray-300">Add location for geographic targeting</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Beautiful Action Buttons -->
                        <div class="mt-10 pt-8 border-t border-gray-200 dark:border-gray-700">
                            <div class="flex flex-col sm:flex-row gap-4">
                                <button type="submit" id="generateQuery" class="beautiful-button-primary flex-1 group relative overflow-hidden bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold py-4 px-8 rounded-xl shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-300">
                                    <div class="absolute inset-0 bg-gradient-to-r from-blue-400 to-purple-400 opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div>
                                    <div class="relative flex items-center justify-center">
                                        <i class="fas fa-search mr-3 text-lg"></i>
                                        <span class="text-lg">Generate Query</span>
                                    </div>
                                </button>
                                <button type="button" id="resetForm" class="beautiful-button-secondary bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800 text-white font-semibold py-4 px-6 rounded-xl shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-300">
                                    <i class="fas fa-undo mr-2"></i>Reset
                                </button>
                                <button type="button" id="exportResults" class="beautiful-button-secondary bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 text-white font-semibold py-4 px-6 rounded-xl shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-300">
                                    <i class="fas fa-download mr-2"></i>Export
                                </button>
                                <button type="button" id="shareQuery" class="beautiful-button-secondary bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700 text-white font-semibold py-4 px-6 rounded-xl shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-300">
                                    <i class="fas fa-share mr-2"></i>Share
                                </button>
                            </div>
                        </div>
                    </form>
                </div>

            </div>
        </div>



        <!-- Bulk Search Content -->
        <div id="bulkSearchContent" class="tab-content hidden">
            <div class="space-y-6">
                <div class="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900 dark:to-purple-900 border border-blue-200 dark:border-blue-700 rounded-xl p-6">
                    <div class="flex items-center mb-3">
                        <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center mr-4">
                            <i class="fas fa-users text-white text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold text-blue-800 dark:text-blue-200">
                                Facebook Bulk People Search
                            </h3>
                            <p class="text-sm text-blue-600 dark:text-blue-300">
                                Find multiple Facebook profiles efficiently
                            </p>
                        </div>
                    </div>
                    <p class="text-sm text-blue-700 dark:text-blue-300">
                        Search for multiple people on Facebook by combining names, interests, locations, and demographics. Perfect for finding friends, colleagues, or networking contacts.
                    </p>
                </div>

                <!-- Bulk Search Form -->
                <div class="space-y-6">
                    <!-- Bulk Search Controls -->
                    <div class="flex justify-end space-x-2 mb-4">
                        <button type="button" id="expandAllSections" class="text-xs text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 px-2 py-1 border border-blue-300 rounded">
                            <i class="fas fa-expand-arrows-alt mr-1"></i>Expand All
                        </button>
                        <button type="button" id="collapseAllSections" class="text-xs text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-300 px-2 py-1 border border-gray-300 rounded">
                            <i class="fas fa-compress-arrows-alt mr-1"></i>Collapse All
                        </button>
                    </div>
                    <!-- Names Section -->
                    <div class="beautiful-section">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-user text-white"></i>
                                </div>
                                <label for="bulkNames" class="text-lg font-semibold text-gray-800 dark:text-gray-200">People Names</label>
                            </div>
                            <button type="button" id="toggleNames" class="beautiful-toggle-btn text-sm text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 flex items-center px-3 py-1 rounded-lg border border-gray-300 dark:border-gray-600 hover:border-blue-400 transition-all duration-200">
                                <i class="fas fa-chevron-up mr-2"></i>
                                <span>Collapse</span>
                            </button>
                        </div>

                        <!-- Collapsible Names Content -->
                        <div id="namesContent" class="transition-all duration-300">
                            <!-- 50/50 Layout: File Upload (Left) + Manual Input (Right) -->
                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <!-- File Upload Option (Left Column) -->
                            <div class="beautiful-upload-area p-6 border-2 border-dashed border-blue-300 dark:border-blue-600 rounded-xl bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900 dark:to-indigo-900 hover:border-blue-400 transition-all duration-300">
                                <div class="text-center">
                                    <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                                        <i class="fas fa-cloud-upload-alt text-2xl text-white"></i>
                                    </div>
                                    <p class="text-sm font-medium text-blue-700 dark:text-blue-300 mb-3">Upload Facebook Friends List</p>
                                    <input type="file" id="namesFileUpload" accept=".csv,.txt,.xlsx,.xls" class="hidden">
                                    <button type="button" id="uploadNamesBtn" class="beautiful-upload-btn bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white px-4 py-2 rounded-lg text-sm font-medium shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-300">
                                        <i class="fas fa-upload mr-2"></i>Choose File
                                    </button>
                                    <p class="text-xs text-blue-600 dark:text-blue-400 mt-2 font-medium">CSV, TXT, Excel supported</p>
                                </div>
                            </div>

                            <!-- Manual Input Option (Right Column) -->
                            <div class="beautiful-input-area">
                                <label class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 block flex items-center">
                                    <i class="fas fa-edit text-green-500 mr-2"></i>
                                    Or enter names manually:
                                </label>
                                <div class="relative">
                                    <textarea id="bulkNames" rows="6" placeholder="John Smith&#10;Sarah Johnson&#10;Michael Brown&#10;Emily Davis&#10;David Wilson" class="beautiful-textarea w-full p-4 border-2 border-gray-200 dark:border-gray-600 rounded-xl focus:border-green-500 focus:ring-4 focus:ring-green-100 dark:focus:ring-green-900 transition-all duration-300 text-gray-900 dark:text-gray-100 bg-white dark:bg-gray-800 font-medium shadow-sm hover:shadow-md resize-none"></textarea>
                                    <div class="absolute bottom-3 right-3 text-xs text-gray-400">
                                        <i class="fas fa-info-circle mr-1"></i>One name per line
                                    </div>
                                </div>
                                <div class="flex justify-between items-center mt-3">
                                    <span id="namesCount" class="text-sm font-medium text-green-600 dark:text-green-400 flex items-center">
                                        <i class="fas fa-users mr-1"></i>0 people
                                    </span>
                                    <button type="button" id="clearNames" class="text-sm text-red-500 hover:text-red-700 font-medium px-3 py-1 rounded-lg hover:bg-red-50 dark:hover:bg-red-900 transition-all duration-200">
                                        <i class="fas fa-trash mr-1"></i>Clear All
                                    </button>
                                </div>
                            </div>
                        </div>
                        </div>
                    </div>

                    <!-- Interests/Fields Section -->
                    <div class="beautiful-section">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-600 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-heart text-white"></i>
                                </div>
                                <label for="bulkTitles" class="text-lg font-semibold text-gray-800 dark:text-gray-200">Interests & Fields</label>
                            </div>
                            <button type="button" id="toggleTitles" class="beautiful-toggle-btn text-sm text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 flex items-center px-3 py-1 rounded-lg border border-gray-300 dark:border-gray-600 hover:border-blue-400 transition-all duration-200">
                                <i class="fas fa-chevron-up mr-2"></i>
                                <span>Collapse</span>
                            </button>
                        </div>

                        <!-- Collapsible Interests Content -->
                        <div id="titlesContent" class="transition-all duration-300">
                            <!-- 50/50 Layout: File Upload (Left) + Manual Input (Right) -->
                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <!-- File Upload Option (Left Column) -->
                            <div class="beautiful-upload-area p-6 border-2 border-dashed border-purple-300 dark:border-purple-600 rounded-xl bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900 dark:to-pink-900 hover:border-purple-400 transition-all duration-300">
                                <div class="text-center">
                                    <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-600 rounded-full flex items-center justify-center mx-auto mb-4">
                                        <i class="fas fa-cloud-upload-alt text-2xl text-white"></i>
                                    </div>
                                    <p class="text-sm font-medium text-purple-700 dark:text-purple-300 mb-3">Upload Interests List</p>
                                    <input type="file" id="titlesFileUpload" accept=".csv,.txt,.xlsx,.xls" class="hidden">
                                    <button type="button" id="uploadTitlesBtn" class="beautiful-upload-btn bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700 text-white px-4 py-2 rounded-lg text-sm font-medium shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-300">
                                        <i class="fas fa-upload mr-2"></i>Choose File
                                    </button>
                                    <p class="text-xs text-purple-600 dark:text-purple-400 mt-2 font-medium">CSV, TXT, Excel supported</p>
                                </div>
                            </div>

                            <!-- Manual Input Option (Right Column) -->
                            <div class="beautiful-input-area">
                                <label class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 block flex items-center">
                                    <i class="fas fa-edit text-purple-500 mr-2"></i>
                                    Or enter interests manually:
                                </label>
                                <div class="relative">
                                    <textarea id="bulkTitles" rows="6" placeholder="Photography&#10;Travel&#10;Music&#10;Technology&#10;Sports&#10;Cooking" class="beautiful-textarea w-full p-4 border-2 border-gray-200 dark:border-gray-600 rounded-xl focus:border-purple-500 focus:ring-4 focus:ring-purple-100 dark:focus:ring-purple-900 transition-all duration-300 text-gray-900 dark:text-gray-100 bg-white dark:bg-gray-800 font-medium shadow-sm hover:shadow-md resize-none"></textarea>
                                    <div class="absolute bottom-3 right-3 text-xs text-gray-400">
                                        <i class="fas fa-info-circle mr-1"></i>One interest per line
                                    </div>
                                </div>
                                <div class="flex justify-between items-center mt-3">
                                    <span id="titlesCount" class="text-sm font-medium text-purple-600 dark:text-purple-400 flex items-center">
                                        <i class="fas fa-heart mr-1"></i>0 interests
                                    </span>
                                    <button type="button" id="clearTitles" class="text-sm text-red-500 hover:text-red-700 font-medium px-3 py-1 rounded-lg hover:bg-red-50 dark:hover:bg-red-900 transition-all duration-200">
                                        <i class="fas fa-trash mr-1"></i>Clear All
                                    </button>
                                </div>
                            </div>
                        </div>
                        </div>
                    </div>

                    <!-- Locations Section -->
                    <div class="beautiful-section">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-gradient-to-br from-orange-500 to-red-600 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-map-marker-alt text-white"></i>
                                </div>
                                <label for="bulkLocations" class="text-lg font-semibold text-gray-800 dark:text-gray-200">Cities & Locations</label>
                            </div>
                            <button type="button" id="toggleLocations" class="beautiful-toggle-btn text-sm text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 flex items-center px-3 py-1 rounded-lg border border-gray-300 dark:border-gray-600 hover:border-blue-400 transition-all duration-200">
                                <i class="fas fa-chevron-up mr-2"></i>
                                <span>Collapse</span>
                            </button>
                        </div>

                        <!-- Collapsible Locations Content -->
                        <div id="locationsContent" class="transition-all duration-300">
                            <!-- 50/50 Layout: File Upload (Left) + Manual Input (Right) -->
                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <!-- File Upload Option (Left Column) -->
                            <div class="beautiful-upload-area p-6 border-2 border-dashed border-orange-300 dark:border-orange-600 rounded-xl bg-gradient-to-br from-orange-50 to-red-50 dark:from-orange-900 dark:to-red-900 hover:border-orange-400 transition-all duration-300">
                                <div class="text-center">
                                    <div class="w-16 h-16 bg-gradient-to-br from-orange-500 to-red-600 rounded-full flex items-center justify-center mx-auto mb-4">
                                        <i class="fas fa-cloud-upload-alt text-2xl text-white"></i>
                                    </div>
                                    <p class="text-sm font-medium text-orange-700 dark:text-orange-300 mb-3">Upload Locations List</p>
                                    <input type="file" id="locationsFileUpload" accept=".csv,.txt,.xlsx,.xls" class="hidden">
                                    <button type="button" id="uploadLocationsBtn" class="beautiful-upload-btn bg-gradient-to-r from-orange-500 to-red-600 hover:from-orange-600 hover:to-red-700 text-white px-4 py-2 rounded-lg text-sm font-medium shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-300">
                                        <i class="fas fa-upload mr-2"></i>Choose File
                                    </button>
                                    <p class="text-xs text-orange-600 dark:text-orange-400 mt-2 font-medium">CSV, TXT, Excel supported</p>
                                </div>
                            </div>

                            <!-- Manual Input Option (Right Column) -->
                            <div class="beautiful-input-area">
                                <label class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3 block flex items-center">
                                    <i class="fas fa-edit text-orange-500 mr-2"></i>
                                    Or enter locations manually:
                                </label>
                                <div class="relative">
                                    <textarea id="bulkLocations" rows="6" placeholder="New York, NY&#10;Los Angeles, CA&#10;London, UK&#10;Toronto, Canada&#10;Sydney, Australia&#10;Tokyo, Japan" class="beautiful-textarea w-full p-4 border-2 border-gray-200 dark:border-gray-600 rounded-xl focus:border-orange-500 focus:ring-4 focus:ring-orange-100 dark:focus:ring-orange-900 transition-all duration-300 text-gray-900 dark:text-gray-100 bg-white dark:bg-gray-800 font-medium shadow-sm hover:shadow-md resize-none"></textarea>
                                    <div class="absolute bottom-3 right-3 text-xs text-gray-400">
                                        <i class="fas fa-info-circle mr-1"></i>One location per line
                                    </div>
                                </div>
                                <div class="flex justify-between items-center mt-3">
                                    <span id="locationsCount" class="text-sm font-medium text-orange-600 dark:text-orange-400 flex items-center">
                                        <i class="fas fa-map-marker-alt mr-1"></i>0 locations
                                    </span>
                                    <button type="button" id="clearLocations" class="text-sm text-red-500 hover:text-red-700 font-medium px-3 py-1 rounded-lg hover:bg-red-50 dark:hover:bg-red-900 transition-all duration-200">
                                        <i class="fas fa-trash mr-1"></i>Clear All
                                    </button>
                                </div>
                            </div>
                        </div>
                        </div>
                    </div>

                    <!-- Facebook Search Matrix Info -->
                    <div class="beautiful-matrix-info bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900 dark:to-indigo-900 border-2 border-blue-200 dark:border-blue-700 rounded-xl p-6 mt-8 shadow-lg">
                        <div class="flex items-center mb-4">
                            <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-network-wired text-white"></i>
                            </div>
                            <h4 class="text-lg font-bold text-blue-800 dark:text-blue-200">
                                Facebook Search Combinations
                            </h4>
                        </div>
                        <div class="grid grid-cols-2 gap-4 text-sm text-blue-700 dark:text-blue-300">
                            <div class="flex items-center justify-between p-3 bg-white dark:bg-gray-800 rounded-lg shadow-sm">
                                <span class="font-medium">👤 People × ❤️ Interests:</span>
                                <span id="namesTitlesMatrix" class="font-bold text-blue-600 dark:text-blue-400">0</span>
                            </div>
                            <div class="flex items-center justify-between p-3 bg-white dark:bg-gray-800 rounded-lg shadow-sm">
                                <span class="font-medium">👤 People × 📍 Locations:</span>
                                <span id="namesLocationsMatrix" class="font-bold text-green-600 dark:text-green-400">0</span>
                            </div>
                            <div class="flex items-center justify-between p-3 bg-white dark:bg-gray-800 rounded-lg shadow-sm">
                                <span class="font-medium">❤️ Interests × 📍 Locations:</span>
                                <span id="titlesLocationsMatrix" class="font-bold text-purple-600 dark:text-purple-400">0</span>
                            </div>
                            <div class="flex items-center justify-between p-3 bg-white dark:bg-gray-800 rounded-lg shadow-sm">
                                <span class="font-medium">👤 People × ❤️ Interests × 📍 Locations:</span>
                                <span id="fullMatrix" class="font-bold text-orange-600 dark:text-orange-400">0</span>
                            </div>
                        </div>
                    </div>

                    <!-- Bulk Search Settings -->
                    <div id="bulkSearchSettingsPanel" class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-sm">
                        <div class="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 cursor-pointer" id="bulkSettingsHeader">
                            <div class="flex items-center">
                                <i class="fas fa-cogs text-gray-600 dark:text-gray-400 mr-2"></i>
                                <h4 class="text-sm font-medium text-gray-900 dark:text-white">Bulk Search Settings</h4>
                            </div>
                            <button type="button" id="toggleBulkSettings" class="px-3 py-1 text-xs bg-gray-500 hover:bg-gray-600 text-white rounded-md transition-colors">
                                <i class="fas fa-chevron-up mr-1" id="bulkSettingsIcon"></i>
                                <span id="bulkSettingsText">Collapse</span>
                            </button>
                        </div>
                        <div id="bulkSettingsContent" class="p-4">
                            <!-- Configuration Name Input -->
                            <div class="mb-4">
                                <input type="text" id="configurationName" placeholder="Enter configuration name..." class="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                <button type="button" id="saveConfiguration" class="mt-2 px-4 py-2 text-sm bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors">
                                    <i class="fas fa-save mr-1"></i>Save
                                </button>
                            </div>

                            <!-- Saved Configurations -->
                            <div class="mb-4">
                                <div class="flex items-center justify-between mb-2">
                                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300">Saved Configurations:</span>
                                    <div class="flex space-x-2">
                                        <button type="button" id="exportConfigurations" class="text-xs text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">
                                            <i class="fas fa-download mr-1"></i>Export
                                        </button>
                                        <button type="button" id="importConfigurations" class="text-xs text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-300">
                                            <i class="fas fa-upload mr-1"></i>Import
                                        </button>
                                        <button type="button" id="clearAllConfigurations" class="text-xs text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300">
                                            <i class="fas fa-trash mr-1"></i>Clear All
                                        </button>
                                    </div>
                                </div>
                                <div id="savedConfigurationsList" class="space-y-2 max-h-32 overflow-y-auto">
                                    <!-- Configurations will be loaded here -->
                                </div>
                            </div>

                            <!-- Quick Actions -->
                            <div class="flex space-x-2">
                                <button type="button" id="loadSampleConfiguration" class="px-3 py-1 text-xs bg-green-600 hover:bg-green-700 text-white rounded-md transition-colors">
                                    <i class="fas fa-play mr-1"></i>Load Sample
                                </button>
                                <button type="button" id="resetConfiguration" class="px-3 py-1 text-xs bg-red-600 hover:bg-red-700 text-white rounded-md transition-colors">
                                    <i class="fas fa-undo mr-1"></i>Reset All
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Facebook Bulk Search Actions -->
                    <div class="flex flex-col sm:flex-row gap-4 mt-8">
                        <button type="button" id="generateBulkQueries" class="beautiful-generate-btn flex-1 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white p-4 rounded-xl font-semibold text-lg shadow-xl hover:shadow-2xl transform hover:-translate-y-1 transition-all duration-300 flex items-center justify-center">
                            <i class="fas fa-users mr-3 text-xl"></i>
                            <span>Generate Facebook Searches</span>
                            <i class="fas fa-arrow-right ml-3"></i>
                        </button>
                        <button type="button" id="clearAllBulk" class="beautiful-clear-btn bg-gradient-to-r from-red-500 to-pink-600 hover:from-red-600 hover:to-pink-700 text-white p-4 rounded-xl font-semibold shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300 flex items-center justify-center">
                            <i class="fas fa-trash mr-2"></i>Clear All
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Visual Query Builder Content -->
        <div id="visualBuilderSearchContent" class="tab-content hidden">
            <div class="space-y-6">
                <div class="bg-purple-50 dark:bg-purple-900 border border-purple-200 dark:border-purple-700 rounded-lg p-4">
                    <h3 class="text-lg font-semibold text-purple-800 dark:text-purple-200 mb-2">
                        <i class="fas fa-puzzle-piece mr-2"></i>Visual Query Builder
                    </h3>
                    <p class="text-sm text-purple-700 dark:text-purple-300">
                        Build your search query visually by dragging and dropping components. Perfect for complex searches.
                    </p>
                </div>

                <!-- Visual Query Builder Form -->
                <div class="mb-4">
                    <h4 class="font-medium mb-2">Drag and drop to build your query:</h4>
                    <div class="flex flex-wrap gap-2 mb-4">
                        <button type="button" class="query-add-btn bg-blue-500 text-white px-3 py-1 rounded text-sm hover:bg-blue-600" data-field="name">
                            <i class="fas fa-plus mr-1"></i>Add Name
                        </button>
                        <button type="button" class="query-add-btn bg-green-500 text-white px-3 py-1 rounded text-sm hover:bg-green-600" data-field="jobTitle">
                            <i class="fas fa-plus mr-1"></i>Add Interests
                        </button>
                        <button type="button" class="query-add-btn bg-purple-500 text-white px-3 py-1 rounded text-sm hover:bg-purple-600" data-field="location">
                            <i class="fas fa-plus mr-1"></i>Add Location
                        </button>
                        <button type="button" class="query-add-btn bg-orange-500 text-white px-3 py-1 rounded text-sm hover:bg-orange-600" data-field="field">
                            <i class="fas fa-plus mr-1"></i>Add Field
                        </button>

                        <button type="button" id="clearQueryBuilder" class="bg-gray-500 text-white px-3 py-1 rounded text-sm hover:bg-gray-600">
                            <i class="fas fa-trash mr-1"></i>Clear All
                        </button>
                    </div>
                </div>
                <div id="queryBuilder" class="query-builder">
                    <div class="query-builder-empty">
                        <i class="fas fa-mouse-pointer text-4xl mb-2"></i>
                        <div>Click "Add" buttons above to build your query visually</div>
                        <div class="text-xs mt-1">Drag blocks to reorder • Click × to remove</div>
                    </div>
                </div>
                <div class="mt-4">
                    <label class="block text-sm font-medium mb-2">Generated Query:</label>
                    <div id="visualQueryOutput" class="bg-gray-100 dark:bg-gray-700 p-3 rounded border font-mono text-sm min-h-[40px]">
                        Query will appear here...
                    </div>
                </div>

                <!-- Visual Query Builder Actions -->
                <div class="flex flex-col sm:flex-row gap-3 mt-6">
                    <button type="button" id="generateVisualQuery" class="flex-1 bg-facebook-blue text-white p-3 rounded hover:bg-facebook-darkBlue transition duration-300">
                        <i class="fas fa-puzzle-piece mr-2"></i>Generate Visual Query
                    </button>
                    <button type="button" id="clearVisualBuilder" class="bg-red-500 text-white p-3 rounded hover:bg-red-600 transition duration-300">
                        <i class="fas fa-trash mr-2"></i>Clear Builder
                    </button>
                </div>

                <!-- Visual Query Results -->
                <div id="visualQueryResults" class="hidden mt-6">
                    <div class="bg-green-50 dark:bg-green-900 border border-green-200 dark:border-green-700 rounded-lg p-4">
                        <h4 class="text-sm font-semibold text-green-800 dark:text-green-200 mb-3">
                            <i class="fas fa-check-circle mr-2"></i>Generated Query Ready
                        </h4>

                        <div class="mb-4">
                            <label class="text-xs font-medium text-green-700 dark:text-green-300 mb-2 block">Your Visual Query:</label>
                            <textarea id="visualQueryDisplay" rows="3" class="w-full bg-white dark:bg-gray-800 border border-green-300 dark:border-green-600 rounded p-3 font-mono text-sm text-gray-800 dark:text-gray-200 resize-none" placeholder="Generated query will appear here..."></textarea>
                            <div class="text-xs text-green-600 dark:text-green-400 mt-1">
                                <i class="fas fa-info-circle mr-1"></i>You can edit this query before searching
                            </div>
                        </div>

                        <div class="flex flex-col sm:flex-row gap-3">
                            <button type="button" id="searchWithVisualQuery" class="flex-1 bg-green-600 text-white p-3 rounded hover:bg-green-700 transition duration-300">
                                <i class="fas fa-search mr-2"></i>Search with This Query
                            </button>
                            <button type="button" id="copyVisualQuery" class="bg-gray-600 text-white p-3 rounded hover:bg-gray-700 transition duration-300">
                                <i class="fas fa-copy mr-2"></i>Copy Query
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- WhatsApp Live Chat Button -->
    <a href="#"
       onclick="window.open(window.APP_CONFIG.whatsappSupportUrl, '_blank'); return false;"
       class="whatsapp-chat-btn">
        <i class="fab fa-whatsapp"></i>
        <div class="whatsapp-chat-tooltip">Need help? Chat with us!</div>
    </a>

    <!-- Footer -->
    <footer class="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 mt-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <p class="text-center text-sm text-gray-600 dark:text-gray-400">
                AI Facebook Graph Search © 2024 All Rights Reserved
            </p>
        </div>
    </footer>

    <script>
        // Theme Toggle Functionality
        const toggle = document.getElementById('toggle');
        const html = document.documentElement;

        // Check for saved theme preference or default to 'dark'
        const currentTheme = localStorage.getItem('theme') || 'dark';
        html.classList.toggle('dark', currentTheme === 'dark');
        toggle.checked = currentTheme === 'light';

        toggle.addEventListener('change', function() {
            if (this.checked) {
                html.classList.remove('dark');
                localStorage.setItem('theme', 'light');
            } else {
                html.classList.add('dark');
                localStorage.setItem('theme', 'dark');
            }
        });

        // Tab Functionality
        const tabs = document.querySelectorAll('.modern-tab');
        const tabContents = document.querySelectorAll('.tab-content');

        tabs.forEach(tab => {
            tab.addEventListener('click', function() {
                // Remove active class from all tabs
                tabs.forEach(t => t.classList.remove('active'));
                // Add active class to clicked tab
                this.classList.add('active');

                // Hide all tab contents
                tabContents.forEach(content => content.classList.add('hidden'));

                // Show corresponding tab content
                const tabId = this.id.replace('Tab', 'Content');
                const targetContent = document.getElementById(tabId);
                if (targetContent) {
                    targetContent.classList.remove('hidden');
                }
            });
        });

        // Advanced Options Toggle
        document.getElementById('advancedOptionsToggle').addEventListener('click', function() {
            const content = document.getElementById('advancedOptionsContent');
            const chevron = document.getElementById('advancedChevron');

            content.classList.toggle('hidden');
            chevron.classList.toggle('rotate-180');
        });



        // Template Selection
        document.querySelectorAll('.template-card').forEach(card => {
            card.addEventListener('click', function() {
                const template = this.dataset.template;
                applyTemplate(template);
            });
        });

        // Apply Template Function
        function applyTemplate(templateName) {
            const templates = {
                'marketing-manager': {
                    searchType: 'people',
                    field: 'Marketing',
                    jobTitle: 'Marketing Manager'
                },
                'sales-director': {
                    searchType: 'people',
                    field: 'Sales',
                    jobTitle: 'Sales Director'
                },
                'software-engineer': {
                    searchType: 'people',
                    field: 'Technology',
                    jobTitle: 'Software Engineer'
                },
                'hr-professional': {
                    searchType: 'people',
                    field: 'Human Resources',
                    jobTitle: 'HR Manager'
                },
                'startup-founder': {
                    searchType: 'people',
                    field: 'Entrepreneurship',
                    jobTitle: 'Founder'
                },
                'finance-executive': {
                    searchType: 'people',
                    field: 'Finance',
                    jobTitle: 'CFO'
                }
            };

            const template = templates[templateName];
            if (template) {
                // Apply template values
                if (template.searchType) document.getElementById('searchType').value = template.searchType;
                if (template.field) document.getElementById('field').value = template.field;
                if (template.jobTitle) document.getElementById('jobTitle').value = template.jobTitle;

                // Update floating labels
                document.querySelectorAll('.modern-input').forEach(input => {
                    const label = input.nextElementSibling;
                    if (label && label.classList.contains('floating-label') && input.value) {
                        label.style.top = '0';
                        label.style.fontSize = '12px';
                        label.style.color = '#1877F2';
                        label.style.fontWeight = '500';
                    }
                });

                showNotification(`Applied ${templateName.replace('-', ' ')} template`, 'success');
            }
        }

        // ===== SEARCH HISTORY & SAVED SEARCHES FUNCTIONALITY =====

        // Load search history from localStorage
        function loadSearchHistory() {
            try {
                const saved = localStorage.getItem('facebookSearchHistory');
                return saved ? JSON.parse(saved) : [];
            } catch (e) {
                console.error('Error loading search history:', e);
                return [];
            }
        }

        // Save search history to localStorage
        function saveSearchHistory(history) {
            try {
                // Keep only the last 50 searches
                const limitedHistory = history.slice(-50);
                localStorage.setItem('facebookSearchHistory', JSON.stringify(limitedHistory));
            } catch (e) {
                console.error('Error saving search history:', e);
            }
        }

        // Load saved searches from localStorage
        function loadSavedSearches() {
            try {
                const saved = localStorage.getItem('facebookSavedSearches');
                return saved ? JSON.parse(saved) : [];
            } catch (e) {
                console.error('Error loading saved searches:', e);
                return [];
            }
        }

        // Initialize sample search history if none exists
        function initializeSampleSearchHistory() {
            const existingHistory = JSON.parse(localStorage.getItem('facebookSearchHistory') || '[]');
            if (existingHistory.length === 0) {
                const sampleHistory = [
                    {
                        searchType: 'people',
                        field: 'Photography',
                        name: 'John Smith',
                        jobTitle: 'Love photography',
                        country: 'United States',
                        state: 'California',
                        city: 'Los Angeles',
                        searchEngine: 'facebook',
                        query: 'John Smith Love photography Photography Los Angeles California United States',
                        url: window.APP_CONFIG.facebookSearchBaseUrl + 'people/?q=John%20Smith%20Love%20photography%20Photography%20Los%20Angeles%20California%20United%20States',
                        timestamp: Date.now() - 86400000 // 1 day ago
                    },
                    {
                        searchType: 'people',
                        field: 'Travel',
                        name: 'Sarah Johnson',
                        jobTitle: 'Travel enthusiast',
                        country: 'United States',
                        state: 'New York',
                        city: 'New York',
                        searchEngine: 'facebook',
                        query: 'Sarah Johnson Travel enthusiast Travel New York New York United States',
                        url: window.APP_CONFIG.facebookSearchBaseUrl + 'people/?q=Sarah%20Johnson%20Travel%20enthusiast%20Travel%20New%20York%20New%20York%20United%20States',
                        timestamp: Date.now() - ********* // 2 days ago
                    },
                    {
                        searchType: 'people',
                        field: 'Music',
                        name: 'Mike Chen',
                        jobTitle: 'Music lover',
                        country: 'United States',
                        state: 'Texas',
                        city: 'Austin',
                        searchEngine: 'facebook',
                        query: 'Mike Chen Music lover Music Austin Texas United States',
                        url: window.APP_CONFIG.facebookSearchBaseUrl + 'people/?q=Mike%20Chen%20Music%20lover%20Music%20Austin%20Texas%20United%20States',
                        timestamp: Date.now() - ********* // 3 days ago
                    }
                ];
                saveSearchHistory(sampleHistory);
            }
        }

        // Save searches to localStorage
        function saveSavedSearches(searches) {
            try {
                localStorage.setItem('facebookSavedSearches', JSON.stringify(searches));
            } catch (e) {
                console.error('Error saving searches:', e);
            }
        }

        // Add search to history
        function addToSearchHistory(searchData) {
            const history = loadSearchHistory();
            const newEntry = {
                ...searchData,
                timestamp: new Date().toISOString(),
                id: Date.now()
            };

            history.push(newEntry);
            saveSearchHistory(history);
            updateSearchHistoryDisplay();
        }

        // Save current search
        function saveCurrentSearch() {
            const searchName = prompt('Enter a name for this search:');
            if (!searchName) return;

            const currentSearch = {
                name: searchName,
                searchType: document.getElementById('searchType')?.value || '',
                field: document.getElementById('field')?.value || '',
                name: document.getElementById('name')?.value || '',
                jobTitle: document.getElementById('jobTitle')?.value || '',
                emailProvider: document.getElementById('emailProvider')?.value || '',
                country: document.getElementById('country')?.value || '',
                state: document.getElementById('state')?.value || '',
                city: document.getElementById('city')?.value || '',
                createdAt: new Date().toISOString(),
                id: Date.now()
            };

            const savedSearches = loadSavedSearches();
            savedSearches.push(currentSearch);
            saveSavedSearches(savedSearches);
            updateSavedSearchesDisplay();
            showSuccess(`Search "${searchName}" saved successfully!`);
        }

        // Clear search history
        function clearSearchHistory() {
            if (confirm('Are you sure you want to clear all search history? This action cannot be undone.')) {
                localStorage.removeItem('facebookSearchHistory');
                updateSearchHistoryDisplay();
                showSuccess('Search history cleared successfully!');
            }
        }

        // Update search history display
        function updateSearchHistoryDisplay() {
            const history = loadSearchHistory();
            const container = document.getElementById('recentSearchesList');

            if (!container) return;

            if (history.length === 0) {
                container.innerHTML = '<div class="text-center text-gray-500 dark:text-gray-400 py-4">No recent searches</div>';
            } else {
                const historyHtml = history.slice(-10).reverse().map(search => `
                    <div class="flex justify-between items-center p-2 hover:bg-gray-50 dark:hover:bg-gray-700 rounded">
                        <div>
                            <div class="font-medium text-sm">${search.searchType?.toUpperCase() || 'SEARCH'} - ${search.field || search.name || 'No details'}</div>
                            <div class="text-xs text-gray-500 dark:text-gray-400">
                                ${search.query || 'No query'} • ${new Date(search.timestamp).toLocaleString()}
                            </div>
                        </div>
                        <div class="flex space-x-1">
                            <button onclick="loadSearchFromHistoryById(${search.id})" class="text-xs bg-blue-600 text-white px-2 py-1 rounded hover:bg-blue-700">
                                Load
                            </button>
                            <button onclick="addToFavorites(${JSON.stringify(search).replace(/"/g, '&quot;')})" class="text-xs bg-yellow-600 text-white px-2 py-1 rounded hover:bg-yellow-700">
                                ★
                            </button>
                        </div>
                    </div>
                `).join('');
                container.innerHTML = historyHtml;
            }
        }

        // Update saved searches display
        function updateSavedSearchesDisplay() {
            const savedSearches = loadSavedSearches();
            const container = document.getElementById('savedSearchesList');

            if (!container) return;

            if (savedSearches.length === 0) {
                container.innerHTML = '<div class="text-center text-gray-500 dark:text-gray-400 py-4">No saved searches</div>';
            } else {
                const savedHtml = savedSearches.map(search => `
                    <div class="flex justify-between items-center p-2 hover:bg-gray-50 dark:hover:bg-gray-700 rounded">
                        <div>
                            <div class="font-medium text-sm">${search.name}</div>
                            <div class="text-xs text-gray-500 dark:text-gray-400">
                                ${search.engine?.toUpperCase() || 'Unknown'} - ${search.resultType === 'image' ? 'Image' : 'Web'} •
                                ${search.excludeTerms?.length || 0} excludes • ${new Date(search.createdAt).toLocaleDateString()}
                            </div>
                        </div>
                        <div class="flex space-x-1">
                            <button onclick="loadSavedSearch(${search.id})" class="text-xs bg-blue-600 text-white px-2 py-1 rounded hover:bg-blue-700">
                                Load
                            </button>
                            <button onclick="deleteSavedSearch(${search.id})" class="text-xs bg-red-600 text-white px-2 py-1 rounded hover:bg-red-700">
                                ×
                            </button>
                        </div>
                    </div>
                `).join('');
                container.innerHTML = savedHtml;
            }
        }

        // Load search from history by ID (for Recent Searches)
        function loadSearchFromHistoryById(searchId) {
            const history = loadSearchHistory();
            const search = history.find(s => s.id === searchId);
            if (search) {
                // Load form field values
                if (search.searchType) document.getElementById('searchType').value = search.searchType;
                if (search.field) document.getElementById('field').value = search.field;
                if (search.name) document.getElementById('name').value = search.name;
                if (search.jobTitle) document.getElementById('jobTitle').value = search.jobTitle;
                if (search.emailProvider) document.getElementById('emailProvider').value = search.emailProvider;
                if (search.country) document.getElementById('country').value = search.country;
                if (search.state) document.getElementById('state').value = search.state;
                if (search.city) document.getElementById('city').value = search.city;

                // Update AI suggestions based on loaded data
                updateAISuggestions();

                // Update name validation
                const nameInput = document.getElementById('name');
                if (nameInput && nameInput.value) {
                    const nameValidation = nameInput.parentElement.querySelector('.name-validation');
                    const namePattern = /^[a-zA-Z]{2,}\s+[a-zA-Z]{2,}(\s+[a-zA-Z]{2,})*$/;
                    const isValid = namePattern.test(nameInput.value);

                    if (isValid) {
                        nameInput.classList.add('valid');
                        if (nameValidation) nameValidation.classList.remove('hidden');
                    }
                }

                showSuccess('Search loaded from history!');
            }
        }

        // Load saved search
        function loadSavedSearch(searchId) {
            const savedSearches = loadSavedSearches();
            const search = savedSearches.find(s => s.id === searchId);
            if (search) {
                // Load form field values
                if (search.searchType) document.getElementById('searchType').value = search.searchType;
                if (search.field) document.getElementById('field').value = search.field;
                if (search.name) document.getElementById('name').value = search.name;
                if (search.jobTitle) document.getElementById('jobTitle').value = search.jobTitle;
                if (search.emailProvider) document.getElementById('emailProvider').value = search.emailProvider;
                if (search.country) document.getElementById('country').value = search.country;
                if (search.state) document.getElementById('state').value = search.state;
                if (search.city) document.getElementById('city').value = search.city;

                // Update AI suggestions based on loaded data
                updateAISuggestions();

                // Update name validation
                const nameInput = document.getElementById('name');
                if (nameInput && nameInput.value) {
                    const nameValidation = nameInput.parentElement.querySelector('.name-validation');
                    const namePattern = /^[a-zA-Z]{2,}\s+[a-zA-Z]{2,}(\s+[a-zA-Z]{2,})*$/;
                    const isValid = namePattern.test(nameInput.value);

                    if (isValid) {
                        nameInput.classList.add('valid');
                        if (nameValidation) nameValidation.classList.remove('hidden');
                    }
                }

                showSuccess(`Saved search "${search.name}" loaded!`);
            }
        }

        // Delete saved search
        function deleteSavedSearch(searchId) {
            const savedSearches = loadSavedSearches();
            const search = savedSearches.find(s => s.id === searchId);
            if (search && confirm(`Are you sure you want to delete "${search.name}"?`)) {
                const updatedSearches = savedSearches.filter(s => s.id !== searchId);
                saveSavedSearches(updatedSearches);
                updateSavedSearchesDisplay();
                showSuccess(`Search "${search.name}" deleted!`);
            }
        }

        // Make functions globally accessible
        window.loadSearchFromHistoryById = loadSearchFromHistoryById;
        window.loadSavedSearch = loadSavedSearch;
        window.deleteSavedSearch = deleteSavedSearch;

        // Search History Toggle (moved to DOMContentLoaded)

        // Search History Event Listeners
        document.getElementById('saveCurrentSearch').addEventListener('click', saveCurrentSearch);
        document.getElementById('clearHistory').addEventListener('click', clearSearchHistory);

        // History Tab Switching
        document.querySelectorAll('.history-tab').forEach(tab => {
            tab.addEventListener('click', function() {
                // Remove active class from all tabs
                document.querySelectorAll('.history-tab').forEach(t => {
                    t.classList.remove('active', 'border-facebook-blue', 'text-facebook-blue');
                    t.classList.add('border-transparent', 'text-gray-500');
                });

                // Add active class to clicked tab
                this.classList.add('active', 'border-facebook-blue', 'text-facebook-blue');
                this.classList.remove('border-transparent', 'text-gray-500');

                // Show/hide appropriate content
                const isRecentTab = this.textContent.trim() === 'Recent Searches';
                document.getElementById('recentSearchesList').classList.toggle('hidden', !isRecentTab);
                document.getElementById('savedSearchesList').classList.toggle('hidden', isRecentTab);
            });
        });

        // Initialize search history displays
        function initializeSearchHistory() {
            updateSearchHistoryDisplay();
            updateSavedSearchesDisplay();
        }

        // Initialize search history when settings are initialized
        initializeSearchHistory();



        // Generate Query Button Handler
        document.getElementById('generateQuery').addEventListener('click', function() {
            // Check license before allowing search
            if (!checkLicenseBeforeAction('search functionality')) {
                return;
            }

            const searchType = document.getElementById('searchType').value;
            const field = document.getElementById('field').value;
            const name = document.getElementById('name').value;
            const jobTitle = document.getElementById('jobTitle').value;
            const country = document.getElementById('country')?.value || '';
            const state = document.getElementById('state')?.value || '';
            const city = document.getElementById('city')?.value || '';

            let searchUrl = '';
            let searchTerms = [];
            let locationTerms = [];

            // Build search terms
            if (name) searchTerms.push(`"${name}"`);
            if (jobTitle) searchTerms.push(`"${jobTitle}"`);
            if (field) searchTerms.push(`"${field}"`);

            // Build location terms
            if (city) locationTerms.push(`"${city}"`);
            if (state) locationTerms.push(`"${state}"`);
            if (country) locationTerms.push(`"${country}"`);

            // Always use direct Facebook search - external search engines don't index Facebook profiles properly
            searchUrl = window.APP_CONFIG.facebookSearchBaseUrl;
            let fbSearchTerms = [];

            switch(searchType) {
                case 'people':
                    searchUrl += 'people/?q=';
                    if (name) fbSearchTerms.push(name);
                    if (jobTitle) fbSearchTerms.push(jobTitle);
                    if (field) fbSearchTerms.push(field);
                    break;
                case 'pages':
                    searchUrl += 'pages/?q=';
                    if (name) fbSearchTerms.push(name);
                    if (field) fbSearchTerms.push(field);
                    break;
                case 'groups':
                    searchUrl += 'groups/?q=';
                    if (name) fbSearchTerms.push(name);
                    if (field) fbSearchTerms.push(field);
                    break;
                case 'posts':
                    searchUrl += 'posts/?q=';
                    if (name) fbSearchTerms.push(name);
                    if (field) fbSearchTerms.push(field);
                    if (jobTitle) fbSearchTerms.push(jobTitle);
                    break;
                case 'events':
                    searchUrl += 'events/?q=';
                    if (name) fbSearchTerms.push(name);
                    if (field) fbSearchTerms.push(field);
                    break;
                case 'marketplace':
                    searchUrl += 'marketplace/?q=';
                    if (field) fbSearchTerms.push(field);
                    break;
                default:
                    searchUrl += 'top/?q=';
                    if (name) fbSearchTerms.push(name);
                    if (field) fbSearchTerms.push(field);
                    if (jobTitle) fbSearchTerms.push(jobTitle);
            }

            // Add location terms to Facebook search
            if (city) fbSearchTerms.push(city);
            if (state) fbSearchTerms.push(state);
            if (country) fbSearchTerms.push(country);

            searchUrl += encodeURIComponent(fbSearchTerms.join(' '));

            // Open search in new tab
            window.open(searchUrl, '_blank');

            // Add to search history
            const searchData = {
                searchType: searchType,
                field: field,
                name: name,
                jobTitle: jobTitle,
                emailProvider: document.getElementById('emailProvider')?.value || '',
                country: country,
                state: state,
                city: city,
                searchEngine: 'facebook',
                query: fbSearchTerms.join(' '),
                url: searchUrl
            };
            addToSearchHistory(searchData);

            // Show success notification
            showNotification('Facebook search query generated successfully!', 'success');
        });

        // Reset Form Handler
        document.getElementById('resetForm').addEventListener('click', function() {
            document.getElementById('searchForm').reset();
            // Reset floating labels
            document.querySelectorAll('.floating-label').forEach(label => {
                label.style.top = '50%';
                label.style.fontSize = '16px';
                label.style.color = 'rgba(107, 114, 128, 0.8)';
                label.style.fontWeight = 'normal';
            });
            showNotification('Form reset successfully', 'info');
        });

        // Export Results Handler
        document.getElementById('exportResults').addEventListener('click', function() {
            const formData = {
                searchType: document.getElementById('searchType').value,
                field: document.getElementById('field').value,
                name: document.getElementById('name').value,
                jobTitle: document.getElementById('jobTitle').value,
                timestamp: new Date().toISOString()
            };

            // Create CSV content
            const csvContent = "data:text/csv;charset=utf-8," +
                "Search Type,Field,Name,Job Title,Timestamp\n" +
                `"${formData.searchType}","${formData.field}","${formData.name}","${formData.jobTitle}","${formData.timestamp}"`;

            // Download CSV
            const encodedUri = encodeURI(csvContent);
            const link = document.createElement("a");
            link.setAttribute("href", encodedUri);
            link.setAttribute("download", "facebook_search_export.csv");
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            showNotification('Search data exported successfully!', 'success');
        });

        // Share Query Handler
        document.getElementById('shareQuery').addEventListener('click', function() {
            const formData = {
                searchType: document.getElementById('searchType').value,
                field: document.getElementById('field').value,
                name: document.getElementById('name').value,
                jobTitle: document.getElementById('jobTitle').value
            };

            const shareText = `Facebook Graph Search Query:\n` +
                `Search Type: ${formData.searchType}\n` +
                `Field: ${formData.field}\n` +
                `Name: ${formData.name}\n` +
                `Job Title: ${formData.jobTitle}`;

            if (navigator.share) {
                navigator.share({
                    title: 'Facebook Graph Search Query',
                    text: shareText
                });
            } else {
                // Fallback: copy to clipboard
                navigator.clipboard.writeText(shareText).then(() => {
                    showNotification('Search query copied to clipboard!', 'success');
                });
            }
        });

        // Notification System
        function showNotification(message, type = 'info') {
            const bgColor = type === 'success' ? 'bg-green-500' :
                           type === 'error' ? 'bg-red-500' :
                           type === 'info' ? 'bg-blue-500' : 'bg-gray-500';

            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 ${bgColor} text-white px-6 py-3 rounded-lg shadow-lg z-50 transform translate-x-full transition-transform duration-300`;
            notification.innerHTML = `
                <div class="flex items-center">
                    <i class="fas ${type === 'success' ? 'fa-check' : type === 'error' ? 'fa-exclamation-triangle' : 'fa-info-circle'} mr-2"></i>
                    <span>${message}</span>
                </div>
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.classList.remove('translate-x-full');
            }, 100);

            setTimeout(() => {
                notification.classList.add('translate-x-full');
                setTimeout(() => {
                    notification.remove();
                }, 300);
            }, 3000);
        }

        // Floating Labels
        document.querySelectorAll('.modern-input').forEach(input => {
            input.addEventListener('input', function() {
                const label = this.nextElementSibling;
                if (label && label.classList.contains('floating-label')) {
                    if (this.value) {
                        label.style.top = '0';
                        label.style.fontSize = '12px';
                        label.style.color = '#1877F2';
                        label.style.fontWeight = '500';
                    } else {
                        label.style.top = '50%';
                        label.style.fontSize = '16px';
                        label.style.color = 'rgba(107, 114, 128, 0.8)';
                        label.style.fontWeight = 'normal';
                    }
                }
            });
        });

        // Header Button Functionality - REMOVED DUPLICATE (moved to DOMContentLoaded)

        // Search Settings Toggle (handled in DOMContentLoaded)

        // Quick Templates Toggle (moved to DOMContentLoaded)

        // AI-Powered Search Suggestions Functions
        function updateAISuggestions() {
            const currentField = document.getElementById('field')?.value || '';
            const currentJobTitle = document.getElementById('jobTitle')?.value || '';
            const currentName = document.getElementById('name')?.value || '';

            // Get section elements
            const relatedFieldsSection = document.getElementById('relatedFieldsSection');
            const similarJobTitlesSection = document.getElementById('similarJobTitlesSection');

            // Show/hide sections based on input
            const hasFieldInput = currentField.trim().length > 0;
            const hasJobTitleInput = currentJobTitle.trim().length > 0;
            const hasAnyInput = hasFieldInput || hasJobTitleInput || currentName.trim().length > 0;

            if (hasFieldInput) {
                relatedFieldsSection.classList.remove('hidden');
                updateRelatedFields(currentField);
            } else {
                relatedFieldsSection.classList.add('hidden');
            }

            if (hasJobTitleInput) {
                similarJobTitlesSection.classList.remove('hidden');
                updateSimilarJobTitles(currentJobTitle);
            } else {
                similarJobTitlesSection.classList.add('hidden');
            }

            // Always update trending searches
            updateTrendingSearches();
        }

        function updateRelatedFields(currentField) {
            const relatedFieldsContainer = document.getElementById('relatedFields');
            let fields = [];

            // Only show suggestions if there's actual input
            if (currentField.trim().length === 0) {
                relatedFieldsContainer.innerHTML = '';
                return;
            }

            if (currentField.toLowerCase().includes('marketing')) {
                fields = ['Digital Marketing', 'Content Marketing', 'Social Media Marketing', 'Email Marketing', 'Brand Marketing'];
            } else if (currentField.toLowerCase().includes('technology') || currentField.toLowerCase().includes('tech')) {
                fields = ['Software Development', 'Data Science', 'Cloud Computing', 'Cybersecurity', 'AI/Machine Learning'];
            } else if (currentField.toLowerCase().includes('sales')) {
                fields = ['Inside Sales', 'Outside Sales', 'Sales Development', 'Account Management', 'Business Development'];
            } else if (currentField.toLowerCase().includes('finance')) {
                fields = ['Corporate Finance', 'Investment Banking', 'Financial Planning', 'Risk Management', 'Accounting'];
            } else if (currentField.toLowerCase().includes('engineering')) {
                fields = ['Software Engineering', 'Data Engineering', 'DevOps Engineering', 'Systems Engineering', 'Quality Engineering'];
            } else {
                // For other fields, show generic suggestions based on the input
                fields = [`${currentField} Specialist`, `Senior ${currentField}`, `${currentField} Manager`, `${currentField} Director`, `${currentField} Consultant`];
            }

            relatedFieldsContainer.innerHTML = fields.map(field => `
                <span class="bg-blue-100 hover:bg-blue-200 dark:bg-blue-900 dark:hover:bg-blue-800 text-blue-800 dark:text-blue-200 px-3 py-1 rounded-full text-sm cursor-pointer transition-colors duration-200" onclick="applyFieldSuggestion('${field}')">${field}</span>
            `).join('');
        }

        function updateSimilarJobTitles(currentInterest) {
            const similarJobTitlesContainer = document.getElementById('similarJobTitles');
            let interests = [];

            // Only show suggestions if there's actual input
            if (currentInterest.trim().length === 0) {
                similarJobTitlesContainer.innerHTML = '';
                return;
            }

            if (currentInterest.toLowerCase().includes('photography')) {
                interests = ['Portrait Photography', 'Landscape Photography', 'Street Photography', 'Wedding Photography', 'Nature Photography'];
            } else if (currentInterest.toLowerCase().includes('travel')) {
                interests = ['Adventure Travel', 'Cultural Travel', 'Food Tourism', 'Solo Travel', 'Backpacking'];
            } else if (currentInterest.toLowerCase().includes('music')) {
                interests = ['Live Music', 'Classical Music', 'Electronic Music', 'Jazz Music', 'Rock Music'];
            } else if (currentInterest.toLowerCase().includes('fitness')) {
                interests = ['Yoga', 'Running', 'Weightlifting', 'CrossFit', 'Pilates'];
            } else if (currentInterest.toLowerCase().includes('cooking')) {
                interests = ['Baking', 'Grilling', 'Vegetarian Cooking', 'International Cuisine', 'Healthy Cooking'];
            } else {
                // For other interests, show related suggestions
                interests = [`${currentInterest} Enthusiast`, `${currentInterest} Community`, `${currentInterest} Lovers`, `${currentInterest} Club`, `${currentInterest} Group`];
            }

            similarJobTitlesContainer.innerHTML = interests.map(interest => `
                <span class="bg-pink-100 hover:bg-pink-200 dark:bg-pink-900 dark:hover:bg-pink-800 text-pink-800 dark:text-pink-200 px-3 py-1 rounded-full text-sm cursor-pointer transition-colors duration-200" onclick="applyJobTitleSuggestion('${interest}')">${interest}</span>
            `).join('');
        }

        function updateTrendingSearches() {
            const trendingSearchesContainer = document.getElementById('trendingSearches');
            const trendingInterests = [
                'Photography', 'Travel', 'Music', 'Fitness',
                'Cooking', 'Gaming', 'Art & Design', 'Sports'
            ];

            trendingSearchesContainer.innerHTML = trendingInterests.map(interest => `
                <span class="bg-orange-100 hover:bg-orange-200 dark:bg-orange-900 dark:hover:bg-orange-800 text-orange-800 dark:text-orange-200 px-3 py-1 rounded-full text-sm cursor-pointer transition-colors duration-200" onclick="applyTrendingSuggestion('${interest}')">${interest}</span>
            `).join('');
        }

        function applyFieldSuggestion(field) {
            const fieldInput = document.getElementById('field');
            if (fieldInput) {
                fieldInput.value = field;
                showAISuggestionMessage(`Applied field: ${field}`);
                updateAISuggestions();
            }
        }

        function applyJobTitleSuggestion(interest) {
            const jobTitleInput = document.getElementById('jobTitle');
            if (jobTitleInput) {
                jobTitleInput.value = interest;
                showAISuggestionMessage(`Applied interest: ${interest}`);
                updateAISuggestions();
            }
        }

        function applyTrendingSuggestion(interest) {
            const jobTitleInput = document.getElementById('jobTitle');
            if (jobTitleInput) {
                jobTitleInput.value = interest;
                showAISuggestionMessage(`Applied trending interest: ${interest}`);
                updateAISuggestions();
            }
        }

        function showAISuggestionMessage(message) {
            let messageEl = document.getElementById('aiSuggestionMessage');
            if (!messageEl) {
                messageEl = document.createElement('div');
                messageEl.id = 'aiSuggestionMessage';
                messageEl.className = 'fixed top-4 right-4 bg-purple-500 text-white px-4 py-2 rounded-lg shadow-lg z-50 transition-all duration-300';
                document.body.appendChild(messageEl);
            }

            messageEl.textContent = message;
            messageEl.style.display = 'block';

            setTimeout(() => {
                messageEl.style.display = 'none';
            }, 3000);
        }

        // Refresh suggestions button
        document.getElementById('refreshSuggestions').addEventListener('click', function() {
            updateAISuggestions();
            showAISuggestionMessage('AI suggestions refreshed!');
        });

        // Update suggestions when form fields change
        document.getElementById('field').addEventListener('input', function() {
            updateAISuggestions();
        });

        document.getElementById('jobTitle').addEventListener('input', function() {
            updateAISuggestions();
        });

        document.getElementById('name').addEventListener('input', function() {
            updateAISuggestions();
        });

        // Initialize AI suggestions on page load
        updateAISuggestions();

        // Autocomplete Functionality
        const autocompleteData = {
            field: [
                'Photography', 'Travel', 'Music', 'Sports', 'Fitness', 'Cooking',
                'Gaming', 'Art & Design', 'Fashion', 'Technology', 'Books & Reading',
                'Movies & TV', 'Nature & Outdoors', 'Food & Dining', 'Health & Wellness',
                'Pets & Animals', 'Family & Parenting', 'Education', 'Volunteering', 'Business'
            ],
            jobTitle: [
                'Photography', 'Travel', 'Music', 'Sports', 'Fitness', 'Cooking',
                'Gaming', 'Art & Design', 'Fashion', 'Technology', 'Books & Reading',
                'Movies & TV', 'Nature & Outdoors', 'Food & Dining', 'Health & Wellness',
                'Pets & Animals', 'Family & Parenting', 'Education', 'Volunteering', 'Business'
            ],
            name: [
                'John Smith', 'Sarah Johnson', 'Michael Brown', 'Emily Davis', 'David Wilson',
                'Jessica Miller', 'Christopher Taylor', 'Ashley Anderson', 'Matthew Thomas',
                'Amanda Jackson', 'Daniel White', 'Jennifer Harris', 'James Martin', 'Lisa Thompson'
            ],
            country: [
                'United States', 'Canada', 'United Kingdom', 'Germany', 'France', 'Australia',
                'Netherlands', 'Sweden', 'Switzerland', 'Singapore', 'Japan', 'South Korea',
                'India', 'Brazil', 'Mexico', 'Spain', 'Italy', 'Belgium', 'Austria', 'Norway'
            ],
            state: [
                'California', 'New York', 'Texas', 'Florida', 'Illinois', 'Pennsylvania',
                'Ohio', 'Georgia', 'North Carolina', 'Michigan', 'New Jersey', 'Virginia',
                'Washington', 'Arizona', 'Massachusetts', 'Tennessee', 'Indiana', 'Missouri'
            ],
            city: [
                'San Francisco', 'New York', 'Los Angeles', 'Chicago', 'Boston', 'Seattle',
                'Austin', 'Denver', 'Atlanta', 'Miami', 'Dallas', 'Philadelphia', 'Phoenix',
                'San Diego', 'Las Vegas', 'Portland', 'Nashville', 'Charlotte', 'Detroit'
            ]
        };

        function setupAutocomplete(inputId, dataKey) {
            const input = document.getElementById(inputId);
            const suggestionsContainer = document.getElementById(`${inputId}Suggestions`);

            if (!input || !suggestionsContainer) return;

            let currentFocus = -1;
            let suggestions = [];

            input.addEventListener('input', function() {
                const value = this.value.toLowerCase().trim();
                currentFocus = -1;

                if (value.length < 1) {
                    hideSuggestions();
                    return;
                }

                suggestions = autocompleteData[dataKey].filter(item =>
                    item.toLowerCase().includes(value)
                ).slice(0, 8); // Limit to 8 suggestions

                if (suggestions.length > 0) {
                    showSuggestions(suggestions, suggestionsContainer, input);
                } else {
                    hideSuggestions();
                }
            });

            input.addEventListener('keydown', function(e) {
                const suggestionItems = suggestionsContainer.querySelectorAll('.autocomplete-suggestion');

                if (e.key === 'ArrowDown') {
                    e.preventDefault();
                    currentFocus++;
                    if (currentFocus >= suggestionItems.length) currentFocus = 0;
                    setActive(suggestionItems);
                } else if (e.key === 'ArrowUp') {
                    e.preventDefault();
                    currentFocus--;
                    if (currentFocus < 0) currentFocus = suggestionItems.length - 1;
                    setActive(suggestionItems);
                } else if (e.key === 'Enter') {
                    e.preventDefault();
                    if (currentFocus > -1 && suggestionItems[currentFocus]) {
                        suggestionItems[currentFocus].click();
                    }
                } else if (e.key === 'Escape') {
                    hideSuggestions();
                }
            });

            input.addEventListener('blur', function() {
                // Delay hiding to allow click on suggestions
                setTimeout(() => hideSuggestions(), 150);
            });

            function showSuggestions(items, container, inputElement) {
                container.innerHTML = items.map(item => `
                    <div class="autocomplete-suggestion" onclick="selectSuggestion('${inputId}', '${item}')">
                        <i class="fas fa-${getIconForField(dataKey)} text-sm"></i>
                        <span>${item}</span>
                    </div>
                `).join('');
                container.classList.remove('hidden');
            }

            function hideSuggestions() {
                suggestionsContainer.classList.add('hidden');
                currentFocus = -1;
            }

            function setActive(items) {
                items.forEach((item, index) => {
                    if (index === currentFocus) {
                        item.classList.add('active');
                    } else {
                        item.classList.remove('active');
                    }
                });
            }
        }

        function getIconForField(dataKey) {
            const icons = {
                field: 'briefcase',
                jobTitle: 'user-tie',
                name: 'user',
                country: 'globe',
                state: 'map',
                city: 'city'
            };
            return icons[dataKey] || 'tag';
        }

        function selectSuggestion(inputId, value) {
            const input = document.getElementById(inputId);
            const suggestionsContainer = document.getElementById(`${inputId}Suggestions`);

            if (input) {
                input.value = value;
                input.focus();

                // Trigger input event to update AI suggestions
                const event = new Event('input', { bubbles: true });
                input.dispatchEvent(event);

                // Show success message
                showAISuggestionMessage(`Applied: ${value}`);
            }

            if (suggestionsContainer) {
                suggestionsContainer.classList.add('hidden');
            }
        }

        // Initialize autocomplete for all input fields
        setupAutocomplete('field', 'field');
        setupAutocomplete('jobTitle', 'jobTitle');
        setupAutocomplete('name', 'name');
        setupAutocomplete('country', 'country');
        setupAutocomplete('state', 'state');
        setupAutocomplete('city', 'city');

        // Search Preview & Validation Functions
        function updateSearchPreview() {
            const searchType = document.getElementById('searchType')?.value || '';
            const field = document.getElementById('field')?.value || '';
            const jobTitle = document.getElementById('jobTitle')?.value || '';
            const name = document.getElementById('name')?.value || '';
            const country = document.getElementById('country')?.value || '';
            const state = document.getElementById('state')?.value || '';
            const city = document.getElementById('city')?.value || '';

            // Show/hide Search Preview section based on input
            const searchPreviewSection = document.getElementById('searchPreviewSection');
            const hasInput = field || jobTitle || name || country || state || city;
            if (searchPreviewSection) {
                if (hasInput) {
                    searchPreviewSection.classList.remove('hidden');
                } else {
                    searchPreviewSection.classList.add('hidden');
                }
            }

            // Calculate estimated results based on search criteria
            let estimatedMin = 50;
            let estimatedMax = 1000;
            let difficulty = 'Easy';
            let successRate = 90;

            // Adjust estimates based on specificity
            if (searchType) {
                if (searchType === 'people') {
                    estimatedMin = 100;
                    estimatedMax = 2000;
                } else if (searchType === 'pages') {
                    estimatedMin = 20;
                    estimatedMax = 500;
                } else if (searchType === 'groups') {
                    estimatedMin = 10;
                    estimatedMax = 200;
                }
            }

            if (field) {
                estimatedMin = Math.floor(estimatedMin * 0.7);
                estimatedMax = Math.floor(estimatedMax * 0.8);
                difficulty = 'Medium';
                successRate = 85;
            }

            if (jobTitle) {
                estimatedMin = Math.floor(estimatedMin * 0.6);
                estimatedMax = Math.floor(estimatedMax * 0.7);
                difficulty = 'Medium';
                successRate = 80;
            }

            if (name) {
                estimatedMin = Math.floor(estimatedMin * 0.3);
                estimatedMax = Math.floor(estimatedMax * 0.4);
                difficulty = 'Hard';
                successRate = 70;
            }

            if (country || state || city) {
                estimatedMin = Math.floor(estimatedMin * 0.8);
                estimatedMax = Math.floor(estimatedMax * 0.9);
                if (difficulty === 'Easy') difficulty = 'Medium';
                successRate = Math.max(successRate - 5, 60);
            }

            // Update the display
            const estimatedResultsEl = document.getElementById('estimatedResults');
            const searchDifficultyEl = document.getElementById('searchDifficulty');
            const successRateEl = document.getElementById('successRate');

            if (estimatedResultsEl) {
                estimatedResultsEl.textContent = `${estimatedMin}-${estimatedMax} profiles`;
            }

            if (searchDifficultyEl) {
                searchDifficultyEl.textContent = difficulty;
                searchDifficultyEl.className = `text-2xl font-bold ${
                    difficulty === 'Easy' ? 'text-green-600 dark:text-green-400' :
                    difficulty === 'Medium' ? 'text-yellow-600 dark:text-yellow-400' :
                    'text-red-600 dark:text-red-400'
                }`;
            }

            if (successRateEl) {
                successRateEl.textContent = `${successRate}%`;
                successRateEl.className = `text-2xl font-bold ${
                    successRate >= 80 ? 'text-green-600 dark:text-green-400' :
                    successRate >= 60 ? 'text-yellow-600 dark:text-yellow-400' :
                    'text-red-600 dark:text-red-400'
                }`;
            }
        }

        // Update search preview when form fields change
        ['searchType', 'field', 'jobTitle', 'name', 'country', 'state', 'city'].forEach(fieldId => {
            const element = document.getElementById(fieldId);
            if (element) {
                element.addEventListener('input', updateSearchPreview);
                element.addEventListener('change', updateSearchPreview);
            }
        });

        // Initialize search preview on page load
        updateSearchPreview();

        // Facebook Search Templates Data
        const facebookTemplates = [
            {
                name: 'Photography Enthusiasts',
                description: 'Find people passionate about photography',
                data: {
                    searchType: 'people',
                    field: 'Photography',
                    jobTitle: 'Love photography',
                    location: 'United States'
                }
            },
            {
                name: 'Travel Lovers',
                description: 'Find travel enthusiasts and adventurers',
                data: {
                    searchType: 'people',
                    field: 'Travel',
                    jobTitle: 'Travel enthusiast',
                    location: 'United States'
                }
            },
            {
                name: 'Music Fans',
                description: 'Find music lovers and musicians',
                data: {
                    searchType: 'people',
                    field: 'Music',
                    jobTitle: 'Music lover',
                    location: 'United States'
                }
            },
            {
                name: 'Fitness Community',
                description: 'Find fitness enthusiasts and athletes',
                data: {
                    searchType: 'people',
                    field: 'Fitness',
                    jobTitle: 'Fitness enthusiast',
                    location: 'United States'
                }
            },
            {
                name: 'Food & Cooking',
                description: 'Find food lovers and cooking enthusiasts',
                data: {
                    searchType: 'people',
                    field: 'Cooking',
                    jobTitle: 'Love cooking',
                    location: 'United States'
                }
            },
            {
                name: 'Gaming Community',
                description: 'Find gamers and gaming enthusiasts',
                data: {
                    searchType: 'people',
                    field: 'Gaming',
                    jobTitle: 'Gaming enthusiast',
                    location: 'United States'
                }
            },
            {
                name: 'Art & Design',
                description: 'Find artists and design enthusiasts',
                data: {
                    searchType: 'people',
                    field: 'Art & Design',
                    jobTitle: 'Art lover',
                    location: 'United States'
                }
            },
            {
                name: 'Sports Fans',
                description: 'Find sports enthusiasts and athletes',
                data: {
                    searchType: 'people',
                    field: 'Sports',
                    jobTitle: 'Sports fan',
                    location: 'United States'
                }
            }
        ];

        // Populate Templates
        function populateTemplates() {
            const container = document.getElementById('templatesContainer');
            if (!container) return;

            container.innerHTML = '';

            facebookTemplates.forEach((template, index) => {
                const templateCard = document.createElement('div');
                templateCard.className = 'template-card bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700 hover:border-facebook-blue dark:hover:border-facebook-blue transition-all duration-200 cursor-pointer';
                templateCard.innerHTML = `
                    <h4 class="font-semibold text-gray-800 dark:text-gray-200 mb-2">${template.name}</h4>
                    <p class="text-sm text-gray-600 dark:text-gray-400">${template.description}</p>
                `;

                templateCard.addEventListener('click', () => {
                    applyTemplate(template);
                });

                container.appendChild(templateCard);
            });
        }

        // Apply Template Function
        function applyTemplate(template) {
            // Apply template data to form fields
            Object.keys(template.data).forEach(key => {
                const element = document.getElementById(key);
                if (element) {
                    element.value = template.data[key];

                    // Trigger change event for any listeners
                    const event = new Event('change', { bubbles: true });
                    element.dispatchEvent(event);
                }
            });

            // Show success message
            showSuccessMessage(`Applied template: ${template.name}`);

            // Hide templates container
            const container = document.getElementById('templatesContainer');
            const toggleButton = document.getElementById('toggleTemplates');
            const chevron = toggleButton.querySelector('i:last-child');

            container.classList.add('hidden');
            chevron.classList.remove('fa-chevron-up');
            chevron.classList.add('fa-chevron-down');
        }

        // Show success message function
        function showSuccessMessage(message) {
            // Create or update success message element
            let messageEl = document.getElementById('globalMessages');
            if (!messageEl) {
                messageEl = document.createElement('div');
                messageEl.id = 'globalMessages';
                messageEl.className = 'mb-4';
                document.querySelector('.container').insertBefore(messageEl, document.querySelector('.container').firstChild);
            }

            messageEl.innerHTML = `
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative" role="alert">
                    <span class="block sm:inline">${message}</span>
                </div>
            `;

            // Auto-hide after 3 seconds
            setTimeout(() => {
                messageEl.innerHTML = '';
            }, 3000);
        }

        // Alias for showSuccessMessage (removed duplicate - using const declaration below)

        // Show enrichment settings (expandable section like LinkedIn version)
        function showEnrichmentSettings() {
            const enrichmentContainer = document.getElementById('enrichmentSettingsContainer');

            if (enrichmentContainer) {
                // Toggle the visibility
                enrichmentContainer.classList.toggle('hidden');

                // Update the chevron icon
                const chevron = document.querySelector('#enrichmentSettings i.fa-chevron-down, #enrichmentSettings i.fa-chevron-up');
                if (chevron) {
                    if (enrichmentContainer.classList.contains('hidden')) {
                        chevron.className = 'fas fa-chevron-down ml-1';
                    } else {
                        chevron.className = 'fas fa-chevron-up ml-1';
                    }
                }
            }
        }

        function resetEnrichmentSettings() {
            // Reset all toggles to default state
            const container = document.getElementById('enrichmentSettingsContainer');
            if (container) {
                const checkboxes = container.querySelectorAll('input[type="checkbox"]');
                checkboxes.forEach(checkbox => {
                    checkbox.checked = true;
                });

                const selects = container.querySelectorAll('select');
                if (selects[0]) selects[0].selectedIndex = 0; // Clearbit
                if (selects[1]) selects[1].selectedIndex = 0; // Hunter.io

                const slider = container.querySelector('input[type="range"]');
                if (slider) {
                    slider.value = 75;
                    const percentSpan = container.querySelector('.text-sm.font-medium');
                    if (percentSpan) percentSpan.textContent = '75%';
                }

                showSuccessMessage('Enrichment settings reset to defaults!');
            }
        }

        function saveEnrichmentSettings() {
            // Save settings to localStorage
            const container = document.getElementById('enrichmentSettingsContainer');
            if (container) {
                const settings = {
                    emailPatterns: container.querySelector('input[type="checkbox"]').checked,
                    socialMedia: {},
                    companyData: false,
                    apiPreferences: {
                        primary: container.querySelectorAll('select')[0]?.value || 'Clearbit (Recommended)',
                        fallback: container.querySelectorAll('select')[1]?.value || 'Hunter.io'
                    },
                    dataQuality: {
                        minConfidence: container.querySelector('input[type="range"]')?.value || 75,
                        skipLowQuality: true
                    }
                };

                // Get social media settings
                const socialCheckboxes = container.querySelectorAll('.ml-6 input[type="checkbox"]');
                const socialLabels = ['twitter', 'facebook', 'instagram', 'linkedin', 'tiktok', 'youtube', 'discord', 'github', 'website'];
                socialCheckboxes.forEach((checkbox, index) => {
                    if (socialLabels[index]) {
                        settings.socialMedia[socialLabels[index]] = checkbox.checked;
                    }
                });

                // Get company data setting
                const companyIcon = container.querySelector('.fas.fa-building');
                if (companyIcon) {
                    const companyCheckbox = companyIcon.parentElement.parentElement.querySelector('input[type="checkbox"]');
                    if (companyCheckbox) {
                        settings.companyData = companyCheckbox.checked;
                    }
                }

                localStorage.setItem('facebookEnrichmentSettings', JSON.stringify(settings));
                showSuccessMessage('Enrichment settings saved successfully!');
            }
        }

        function loadEnrichmentSettings() {
            // Load settings from localStorage and apply to UI
            try {
                const savedSettings = localStorage.getItem('facebookEnrichmentSettings');
                if (savedSettings) {
                    const settings = JSON.parse(savedSettings);
                    const container = document.getElementById('enrichmentSettingsContainer');

                    if (container) {
                        // Load email patterns setting
                        const emailCheckbox = container.querySelector('input[type="checkbox"]');
                        if (emailCheckbox && settings.emailPatterns !== undefined) {
                            emailCheckbox.checked = settings.emailPatterns;
                        }

                        // Load social media settings
                        const socialCheckboxes = container.querySelectorAll('.ml-6 input[type="checkbox"]');
                        const socialLabels = ['twitter', 'facebook', 'instagram', 'linkedin', 'tiktok', 'youtube', 'discord', 'github', 'website'];
                        socialCheckboxes.forEach((checkbox, index) => {
                            if (socialLabels[index] && settings.socialMedia && settings.socialMedia[socialLabels[index]] !== undefined) {
                                checkbox.checked = settings.socialMedia[socialLabels[index]];
                            }
                        });

                        // Load company data setting
                        const companyIcon = container.querySelector('.fas.fa-building');
                        if (companyIcon && settings.companyData !== undefined) {
                            const companyCheckbox = companyIcon.parentElement.parentElement.querySelector('input[type="checkbox"]');
                            if (companyCheckbox) {
                                companyCheckbox.checked = settings.companyData;
                            }
                        }

                        // Load API preferences
                        const selects = container.querySelectorAll('select');
                        if (selects[0] && settings.apiPreferences && settings.apiPreferences.primary) {
                            selects[0].value = settings.apiPreferences.primary;
                        }
                        if (selects[1] && settings.apiPreferences && settings.apiPreferences.fallback) {
                            selects[1].value = settings.apiPreferences.fallback;
                        }

                        // Load data quality settings
                        const slider = container.querySelector('input[type="range"]');
                        if (slider && settings.dataQuality && settings.dataQuality.minConfidence) {
                            slider.value = settings.dataQuality.minConfidence;
                            // Update the percentage display using the same method as the event listener
                            const percentSpan = slider.parentElement.querySelector('.text-sm.font-medium');
                            if (percentSpan) {
                                percentSpan.textContent = settings.dataQuality.minConfidence + '%';
                            }
                        }

                        // Load skip low-quality data setting
                        const skipLowQualityCheckboxes = container.querySelectorAll('input[type="checkbox"]');
                        const skipLowQualityCheckbox = skipLowQualityCheckboxes[skipLowQualityCheckboxes.length - 1]; // Last checkbox
                        if (skipLowQualityCheckbox && settings.dataQuality && settings.dataQuality.skipLowQuality !== undefined) {
                            skipLowQualityCheckbox.checked = settings.dataQuality.skipLowQuality;
                        }
                    }
                }
            } catch (error) {
                console.error('Error loading enrichment settings:', error);
            }
        }

        // Favorite Searches functionality
        function initializeFavoriteSearches() {
            const favoriteCards = document.querySelectorAll('.favorite-search-card');
            favoriteCards.forEach(card => {
                card.addEventListener('click', function() {
                    const title = this.querySelector('h4').textContent;
                    applyFavoriteSearch(title);
                });
            });

            // Add to favorites functionality
            const addToFavoritesBtn = document.getElementById('addToFavorites');
            if (addToFavoritesBtn) {
                addToFavoritesBtn.addEventListener('click', function() {
                    addCurrentSearchToFavorites();
                });
            }

            // Manage favorites functionality
            const manageFavoritesBtn = document.getElementById('manageFavorites');
            if (manageFavoritesBtn) {
                manageFavoritesBtn.addEventListener('click', function() {
                    showManageFavoritesModal();
                });
            }
        }

        function applyFavoriteSearch(searchTitle) {
            // Define favorite search templates
            const favoriteTemplates = {
                'Marketing Managers': {
                    searchType: 'people',
                    field: 'Marketing',
                    jobTitle: 'Marketing Manager',
                    country: 'United States'
                },
                'Software Engineers': {
                    searchType: 'people',
                    field: 'Technology',
                    jobTitle: 'Software Engineer',
                    country: 'United States'
                },
                'Sales Directors': {
                    searchType: 'people',
                    field: 'Sales',
                    jobTitle: 'Sales Director',
                    country: 'United States'
                }
            };

            const template = favoriteTemplates[searchTitle];
            if (template) {
                // Apply the template to the form
                Object.keys(template).forEach(key => {
                    const element = document.getElementById(key);
                    if (element) {
                        element.value = template[key];
                        // Trigger change event for any listeners
                        element.dispatchEvent(new Event('change'));
                    }
                });

                // Switch to basic search tab
                switchTab('basic');

                // Show success message
                showSuccessMessage(`Applied favorite search: ${searchTitle}`);

                // Update usage count (stored in localStorage)
                updateFavoriteUsageCount(searchTitle);
            }
        }

        function addCurrentSearchToFavorites() {
            const currentSearch = {
                searchType: document.getElementById('searchType')?.value || '',
                field: document.getElementById('field')?.value || '',
                jobTitle: document.getElementById('jobTitle')?.value || '',
                name: document.getElementById('name')?.value || '',
                country: document.getElementById('country')?.value || '',
                state: document.getElementById('state')?.value || '',
                city: document.getElementById('city')?.value || ''
            };

            // Check if search has enough data
            if (!currentSearch.searchType && !currentSearch.field && !currentSearch.jobTitle) {
                showErrorMessage('Please fill in at least one search field before adding to favorites.');
                return;
            }

            // Generate a name for the favorite
            let favoriteName = '';
            if (currentSearch.jobTitle) favoriteName += currentSearch.jobTitle;
            if (currentSearch.field) favoriteName += (favoriteName ? ' in ' : '') + currentSearch.field;
            if (!favoriteName) favoriteName = 'Custom Search';

            // Save to localStorage
            const favorites = JSON.parse(localStorage.getItem('facebookFavoriteSearches') || '[]');
            const newFavorite = {
                id: Date.now(),
                name: favoriteName,
                description: generateFavoriteDescription(currentSearch),
                data: currentSearch,
                usageCount: 0,
                createdAt: new Date().toISOString()
            };

            favorites.push(newFavorite);
            localStorage.setItem('facebookFavoriteSearches', JSON.stringify(favorites));

            showSuccessMessage(`Added "${favoriteName}" to favorites!`);
            refreshFavoritesList();
        }

        function generateFavoriteDescription(searchData) {
            let description = 'Find ';
            if (searchData.jobTitle) description += searchData.jobTitle.toLowerCase() + 's';
            else if (searchData.field) description += 'professionals in ' + searchData.field.toLowerCase();
            else description += 'people';

            if (searchData.country) description += ' in ' + searchData.country;

            return description;
        }

        function updateFavoriteUsageCount(searchTitle) {
            const favorites = JSON.parse(localStorage.getItem('facebookFavoriteSearches') || '[]');
            const favorite = favorites.find(f => f.name === searchTitle);
            if (favorite) {
                favorite.usageCount = (favorite.usageCount || 0) + 1;
                localStorage.setItem('facebookFavoriteSearches', JSON.stringify(favorites));
            }
        }

        function refreshFavoritesList() {
            // This would refresh the favorites display
            // For now, just show a message that it will be refreshed on page reload
            console.log('Favorites list updated - refresh page to see changes');
        }

        function showManageFavoritesModal() {
            const favorites = JSON.parse(localStorage.getItem('facebookFavoriteSearches') || '[]');

            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
            modal.innerHTML = `
                <div class="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-4xl mx-4 max-h-[90vh] overflow-y-auto">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-xl font-semibold text-gray-900 dark:text-white">Manage Favorite Searches</h3>
                        <button onclick="this.closest('.fixed').remove()" class="text-gray-400 hover:text-gray-600">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>

                    <div class="space-y-4">
                        ${favorites.length === 0 ?
                            '<p class="text-gray-500 dark:text-gray-400 text-center py-8">No custom favorites saved yet.</p>' :
                            favorites.map(fav => `
                                <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <h4 class="font-medium text-gray-900 dark:text-white">${fav.name}</h4>
                                            <p class="text-sm text-gray-600 dark:text-gray-400">${fav.description}</p>
                                            <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Used ${fav.usageCount || 0} times</p>
                                        </div>
                                        <button onclick="deleteFavorite(${fav.id}); this.closest('.bg-gray-50, .dark\\:bg-gray-700').remove();"
                                                class="text-red-500 hover:text-red-700">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            `).join('')
                        }
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
        }

        function deleteFavorite(favoriteId) {
            const favorites = JSON.parse(localStorage.getItem('facebookFavoriteSearches') || '[]');
            const updatedFavorites = favorites.filter(f => f.id !== favoriteId);
            localStorage.setItem('facebookFavoriteSearches', JSON.stringify(updatedFavorites));
            showSuccessMessage('Favorite search deleted successfully!');
        }

        // Modal Functions
        function showLicenseModal(type) {
            // Get current license info
            const licenseInfo = localStorage.getItem('facebookLicenseInfo');
            const hardwareId = generateHardwareId();
            let currentLicenseHtml = '';

            if (licenseInfo) {
                const license = JSON.parse(licenseInfo);
                const expiryDate = new Date(license.expires);
                const currentDate = new Date();
                const daysLeft = Math.ceil((expiryDate - currentDate) / (1000 * 60 * 60 * 24));
                const licenseType = license.type.charAt(0).toUpperCase() + license.type.slice(1);
                const status = daysLeft > 0 ? 'Active' : 'Expired';
                const statusClass = daysLeft > 0 ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';

                currentLicenseHtml = `
                    <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <div>
                            <h5 class="font-medium text-gray-900 dark:text-white">${licenseType} License</h5>
                            <p class="text-sm text-gray-600 dark:text-gray-400">Expires: ${expiryDate.toLocaleDateString('en-US')}</p>
                            <p class="text-xs text-gray-500 dark:text-gray-400">Devices: ${license.currentDevices}/${license.maxDevices}</p>
                        </div>
                        <span class="px-3 py-1 ${statusClass} rounded-full text-sm font-medium">${status}</span>
                    </div>
                `;
            } else {
                currentLicenseHtml = `
                    <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <div>
                            <h5 class="font-medium text-gray-900 dark:text-white">Free Trial</h5>
                            <p class="text-sm text-gray-600 dark:text-gray-400">7 days remaining</p>
                        </div>
                        <span class="px-3 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 rounded-full text-sm font-medium">Trial</span>
                    </div>
                `;
            }

            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
            modal.innerHTML = `
                <div class="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-xl font-semibold text-gray-900 dark:text-white">License Management</h3>
                        <button onclick="this.closest('.fixed').remove()" class="text-gray-400 hover:text-gray-600">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>

                    <!-- Current License -->
                    <div class="mb-6">
                        <h4 class="text-lg font-medium text-gray-900 dark:text-white mb-3">Current License</h4>
                        ${currentLicenseHtml}
                    </div>

                    <!-- Activate License -->
                    <div class="mb-6">
                        <h4 class="text-lg font-medium text-gray-900 dark:text-white mb-3">Activate License</h4>
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Your Email Address</label>
                                <input id="activateEmailInput" type="email" value="<EMAIL>" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">This email must match the license registration</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">License Key</label>
                                <input id="activateLicenseKeyInput" type="text" placeholder="XXXX-XXXX-XXXX-XXXX" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white">
                            </div>
                            <div class="flex items-center text-sm text-gray-600 dark:text-gray-400">
                                <i class="fas fa-desktop mr-2"></i>
                                <span><strong>Hardware ID:</strong> ${hardwareId}</span>
                            </div>
                            <p class="text-xs text-gray-500 dark:text-gray-400">This unique ID identifies your computer for license activation</p>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex space-x-3 mb-6">
                        <button onclick="activateLicense()" class="flex-1 bg-blue-600 text-white px-4 py-3 rounded-md hover:bg-blue-700 font-medium">
                            <i class="fas fa-check mr-2"></i>Activate License
                        </button>
                        <button onclick="exportLicense()" class="flex-1 bg-purple-600 text-white px-4 py-3 rounded-md hover:bg-purple-700 font-medium">
                            <i class="fas fa-download mr-2"></i>Export License
                        </button>
                        <button onclick="importLicense()" class="flex-1 bg-green-600 text-white px-4 py-3 rounded-md hover:bg-green-700 font-medium">
                            <i class="fas fa-upload mr-2"></i>Import License
                        </button>
                    </div>

                    <p class="text-xs text-gray-500 dark:text-gray-400 mb-6 text-center">
                        <strong>Cross-Browser Setup:</strong> Export your license from Chrome/Edge, then Import it in Firefox/Safari.
                    </p>

                    <!-- Upgrade License -->
                    <div class="mb-6">
                        <h4 class="text-lg font-medium text-gray-900 dark:text-white mb-3">
                            <i class="fas fa-arrow-up text-green-600 mr-2"></i>Upgrade or Switch License
                        </h4>
                        <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">Already have a new license key? Upgrade your current license to access more features.</p>
                        <div class="space-y-3">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">New License Key</label>
                                <input id="upgradeLicenseKeyInput" type="text" placeholder="XXXX-XXXX-XXXX-XXXX" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 dark:bg-gray-700 dark:text-white">
                                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Enter your new license key to upgrade</p>
                            </div>
                            <button onclick="upgradeLicense()" class="w-full bg-green-600 text-white px-4 py-3 rounded-md hover:bg-green-700 font-medium">
                                <i class="fas fa-arrow-up mr-2"></i>Upgrade License
                            </button>
                        </div>
                    </div>

                    <!-- Available License Tiers -->
                    <div>
                        <h4 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Available License Tiers</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <!-- Free Trial -->
                            <div class="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                                <div class="flex justify-between items-center mb-3">
                                    <h5 class="font-semibold text-gray-900 dark:text-white">Free Trial</h5>
                                    <span class="text-lg font-bold text-gray-900 dark:text-white">FREE</span>
                                </div>
                                <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">7 days • Limited features</p>
                                <ul class="text-sm space-y-1">
                                    <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Basic search functionality</li>
                                    <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>10 searches per day</li>
                                    <li class="flex items-center"><i class="fas fa-times text-red-500 mr-2"></i>No bulk search</li>
                                    <li class="flex items-center"><i class="fas fa-times text-red-500 mr-2"></i>No analytics</li>
                                    <li class="flex items-center"><i class="fas fa-times text-red-500 mr-2"></i>No export features</li>
                                    <li class="flex items-center"><i class="fas fa-times text-red-500 mr-2"></i>No contact enrichment</li>
                                </ul>
                            </div>

                            <!-- Basic License -->
                            <div class="border border-blue-200 dark:border-blue-600 rounded-lg p-4 bg-blue-50 dark:bg-blue-900">
                                <div class="flex justify-between items-center mb-3">
                                    <h5 class="font-semibold text-blue-900 dark:text-blue-100">Basic License</h5>
                                    <span class="text-lg font-bold text-blue-900 dark:text-blue-100">$29/mo</span>
                                </div>
                                <p class="text-sm text-blue-700 dark:text-blue-300 mb-3">Monthly/Yearly • Core features</p>
                                <ul class="text-sm space-y-1">
                                    <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Unlimited searches</li>
                                    <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Basic analytics</li>
                                    <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Export to CSV</li>
                                    <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Search history</li>
                                    <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Basic contact enrichment (3 platforms)</li>
                                    <li class="flex items-center"><i class="fas fa-times text-red-500 mr-2"></i>No bulk search</li>
                                    <li class="flex items-center"><i class="fas fa-times text-red-500 mr-2"></i>No advanced analytics</li>
                                </ul>
                            </div>

                            <!-- Professional License -->
                            <div class="border border-purple-200 dark:border-purple-600 rounded-lg p-4">
                                <div class="flex justify-between items-center mb-3">
                                    <h5 class="font-semibold text-gray-900 dark:text-white">Professional License</h5>
                                    <span class="text-lg font-bold text-gray-900 dark:text-white">$79/mo</span>
                                </div>
                                <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">Monthly/Yearly • Advanced features</p>
                                <ul class="text-sm space-y-1">
                                    <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Everything in Basic</li>
                                    <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Bulk search functionality</li>
                                    <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Advanced analytics</li>
                                    <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Export to Excel/PDF</li>
                                    <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Visual query builder</li>
                                    <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Full contact enrichment (6 platforms)</li>
                                    <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Email pattern discovery</li>
                                </ul>
                            </div>

                            <!-- Enterprise License -->
                            <div class="border border-yellow-200 dark:border-yellow-600 rounded-lg p-4">
                                <div class="flex justify-between items-center mb-3">
                                    <h5 class="font-semibold text-gray-900 dark:text-white">Enterprise License</h5>
                                    <span class="text-lg font-bold text-gray-900 dark:text-white">$199/mo</span>
                                </div>
                                <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">Yearly • Full features</p>
                                <ul class="text-sm space-y-1">
                                    <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Everything in Professional</li>
                                    <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Premium contact enrichment (all 9 platforms)</li>
                                    <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>White-label branding</li>
                                    <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>API access</li>
                                    <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Priority support</li>
                                    <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Team management</li>
                                </ul>
                                <p class="text-xs text-gray-500 dark:text-gray-400 mt-2">Contact Sales: <EMAIL></p>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        }

        function calculateAnalytics(searchHistory) {
            const now = new Date();
            const thisMonth = searchHistory.filter(search => {
                const searchDate = new Date(search.timestamp);
                return searchDate.getMonth() === now.getMonth() && searchDate.getFullYear() === now.getFullYear();
            });

            const lastMonth = searchHistory.filter(search => {
                const searchDate = new Date(search.timestamp);
                const lastMonthDate = new Date(now.getFullYear(), now.getMonth() - 1);
                return searchDate.getMonth() === lastMonthDate.getMonth() && searchDate.getFullYear() === lastMonthDate.getFullYear();
            });

            // Calculate metrics
            const totalSearches = thisMonth.length;
            const lastMonthSearches = lastMonth.length;
            const searchesTrend = lastMonthSearches > 0 ? ((totalSearches - lastMonthSearches) / lastMonthSearches * 100).toFixed(1) : 0;

            // Simulate success rate based on search complexity
            const complexSearches = thisMonth.filter(search =>
                (search.field && search.field.length > 0) ||
                (search.jobTitle && search.jobTitle.length > 0) ||
                (search.location && search.location.length > 0)
            ).length;
            const successRate = totalSearches > 0 ? Math.min(95, 45 + (complexSearches / totalSearches * 40)).toFixed(1) : 0;
            const successTrend = (Math.random() * 10 - 5).toFixed(1); // Random trend for demo

            // Calculate profile clicks (estimated)
            const profileClicks = Math.floor(totalSearches * (successRate / 100) * 2.5);
            const clicksTrend = (Math.random() * 20 - 10).toFixed(1);

            // Calculate conversion rate
            const conversionRate = profileClicks > 0 ? (profileClicks * 0.15).toFixed(1) : 0;
            const conversionTrend = (Math.random() * 8 - 4).toFixed(1);

            // Calculate ROI
            const avgROI = (profileClicks * 23.5).toFixed(2);
            const roiTrend = (Math.random() * 15 - 7.5).toFixed(1);

            // Generate dynamic insights
            const insights = generateInsights(thisMonth, successRate, totalSearches);
            const recommendations = generateRecommendations(thisMonth, successRate);

            return {
                totalSearches,
                searchesTrend: parseFloat(searchesTrend),
                successRate,
                successTrend: parseFloat(successTrend),
                profileClicks,
                clicksTrend: parseFloat(clicksTrend),
                conversionRate,
                conversionTrend: parseFloat(conversionTrend),
                avgROI,
                roiTrend: parseFloat(roiTrend),
                insights,
                recommendations
            };
        }

        function generateInsights(searches, successRate, totalSearches) {
            const insights = [];

            if (totalSearches === 0) {
                insights.push({
                    title: "No Search Data Available",
                    description: "Start performing searches to see AI-powered insights about your search patterns.",
                    confidence: 100,
                    color: "gray",
                    icon: "info-circle"
                });
                return insights;
            }

            // Performance insight
            if (successRate > 70) {
                insights.push({
                    title: "Excellent Search Performance",
                    description: `Your search success rate of ${successRate}% is above average. You're effectively using search parameters.`,
                    confidence: 90,
                    color: "green",
                    icon: "chart-line"
                });
            } else if (successRate > 50) {
                insights.push({
                    title: "Good Search Performance",
                    description: `Your search success rate of ${successRate}% shows room for improvement. Consider refining your search criteria.`,
                    confidence: 85,
                    color: "yellow",
                    icon: "chart-line"
                });
            } else {
                insights.push({
                    title: "Search Performance Needs Attention",
                    description: `Your search success rate of ${successRate}% is below optimal. Review your search strategy.`,
                    confidence: 95,
                    color: "red",
                    icon: "exclamation-triangle"
                });
            }

            // Usage pattern insight
            const hourCounts = {};
            searches.forEach(search => {
                const hour = new Date(search.timestamp).getHours();
                hourCounts[hour] = (hourCounts[hour] || 0) + 1;
            });

            const peakHour = Object.keys(hourCounts).reduce((a, b) => hourCounts[a] > hourCounts[b] ? a : b, 0);
            if (Object.keys(hourCounts).length > 0) {
                insights.push({
                    title: "Peak Usage Pattern Identified",
                    description: `Most of your searches occur around ${peakHour}:00. Consider this timing for important searches.`,
                    confidence: 78,
                    color: "blue",
                    icon: "clock"
                });
            }

            // Search complexity insight
            const complexSearches = searches.filter(search =>
                (search.field && search.field.length > 0) &&
                (search.jobTitle && search.jobTitle.length > 0)
            ).length;

            if (complexSearches / totalSearches > 0.7) {
                insights.push({
                    title: "Advanced Search Usage",
                    description: `${Math.round(complexSearches / totalSearches * 100)}% of your searches use multiple criteria, leading to better targeting.`,
                    confidence: 88,
                    color: "purple",
                    icon: "brain"
                });
            }

            return insights;
        }

        function generateRecommendations(searches, successRate) {
            const recommendations = [];

            if (searches.length === 0) {
                recommendations.push({
                    title: "Start Your First Search",
                    description: "Begin by performing searches to receive personalized recommendations.",
                    action: "Create your first search query",
                    priority: "HIGH",
                    color: "blue"
                });
                return recommendations;
            }

            // Boolean operators recommendation
            const booleanSearches = searches.filter(search =>
                search.query && (search.query.includes('AND') || search.query.includes('OR') || search.query.includes('"'))
            ).length;

            if (booleanSearches / searches.length < 0.3) {
                recommendations.push({
                    title: "Use More Boolean Operators",
                    description: `Only ${Math.round(booleanSearches / searches.length * 100)}% of your searches use advanced operators. Boolean searches show higher success rates.`,
                    action: "Enable advanced operators in your next search",
                    priority: "HIGH",
                    color: "red"
                });
            }

            // Location targeting recommendation
            const locationSearches = searches.filter(search => search.location && search.location.length > 0).length;
            if (locationSearches / searches.length < 0.5) {
                recommendations.push({
                    title: "Improve Geographic Targeting",
                    description: `${Math.round(locationSearches / searches.length * 100)}% of your searches include location. Geographic targeting improves relevance.`,
                    action: "Add location filters to your searches",
                    priority: "MEDIUM",
                    color: "yellow"
                });
            }

            // Search engine diversity
            const engines = [...new Set(searches.map(search => search.searchEngine || 'google'))];
            if (engines.length === 1) {
                recommendations.push({
                    title: "Diversify Search Engines",
                    description: "You're only using one search engine. Different engines can yield different results.",
                    action: "Try Bing or other search engines",
                    priority: "LOW",
                    color: "green"
                });
            }

            return recommendations;
        }

        function showAnalyticsModal() {
            // Get dynamic analytics data
            const recentSearches = JSON.parse(localStorage.getItem('facebookSearchHistory') || '[]');
            const analytics = calculateAnalytics(recentSearches);

            // Find the container after the favorite searches section
            const favoriteSearchesContainer = document.querySelector('#favoriteSearchesList').closest('.container');
            let analyticsContainer = document.getElementById('analytics-dashboard');

            if (!analyticsContainer) {
                analyticsContainer = document.createElement('div');
                analyticsContainer.id = 'analytics-dashboard';
                analyticsContainer.className = 'mt-6';
                // Insert after the favorite searches section
                if (favoriteSearchesContainer && favoriteSearchesContainer.parentNode) {
                    favoriteSearchesContainer.parentNode.insertBefore(analyticsContainer, favoriteSearchesContainer.nextSibling);
                }
            }

            // Create analytics dashboard content (not modal)
            const analyticsContent = `
                <div class="bg-white dark:bg-gray-800 rounded-lg p-6 w-full border border-gray-200 dark:border-gray-700">
                    <!-- Header -->
                    <div class="flex items-center justify-between mb-6">
                        <div class="flex items-center">
                            <i class="fas fa-chart-line text-blue-600 mr-3 text-xl"></i>
                            <h3 class="text-xl font-semibold text-gray-900 dark:text-white">Advanced Analytics Dashboard</h3>
                        </div>
                        <div class="flex items-center space-x-3">
                            <select class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-700 dark:text-white">
                                <option>This Month</option>
                                <option>Last Month</option>
                                <option>Last 3 Months</option>
                                <option>This Year</option>
                            </select>
                            <button onclick="refreshAnalytics()" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm">
                                <i class="fas fa-sync-alt mr-2"></i>Refresh
                            </button>
                            <button onclick="exportAnalytics()" class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 text-sm">
                                <i class="fas fa-download mr-2"></i>Export
                            </button>
                            <button onclick="hideAnalyticsDashboard()" class="text-gray-400 hover:text-gray-600">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Key Metrics -->
                    <div class="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
                        <div class="bg-white dark:bg-gray-700 p-4 rounded-lg border border-gray-200 dark:border-gray-600">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">Total Searches</p>
                                    <p class="text-2xl font-bold text-gray-900 dark:text-white">${analytics.totalSearches}</p>
                                    <p class="text-xs ${analytics.searchesTrend >= 0 ? 'text-green-500' : 'text-red-500'}">${analytics.searchesTrend >= 0 ? '+' : ''}${analytics.searchesTrend}% from last period</p>
                                </div>
                                <i class="fas fa-search text-blue-500 text-xl"></i>
                            </div>
                        </div>

                        <div class="bg-white dark:bg-gray-700 p-4 rounded-lg border border-gray-200 dark:border-gray-600">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">Success Rate</p>
                                    <p class="text-2xl font-bold text-gray-900 dark:text-white">${analytics.successRate}%</p>
                                    <p class="text-xs ${analytics.successTrend >= 0 ? 'text-green-500' : 'text-red-500'}">${analytics.successTrend >= 0 ? '+' : ''}${analytics.successTrend}% from last period</p>
                                </div>
                                <i class="fas fa-check-circle text-green-500 text-xl"></i>
                            </div>
                        </div>

                        <div class="bg-white dark:bg-gray-700 p-4 rounded-lg border border-gray-200 dark:border-gray-600">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">Profile Clicks</p>
                                    <p class="text-2xl font-bold text-gray-900 dark:text-white">${analytics.profileClicks}</p>
                                    <p class="text-xs ${analytics.clicksTrend >= 0 ? 'text-green-500' : 'text-red-500'}">${analytics.clicksTrend >= 0 ? '+' : ''}${analytics.clicksTrend}% from last period</p>
                                </div>
                                <i class="fas fa-mouse-pointer text-purple-500 text-xl"></i>
                            </div>
                        </div>

                        <div class="bg-white dark:bg-gray-700 p-4 rounded-lg border border-gray-200 dark:border-gray-600">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">Conversion Rate</p>
                                    <p class="text-2xl font-bold text-gray-900 dark:text-white">${analytics.conversionRate}%</p>
                                    <p class="text-xs ${analytics.conversionTrend >= 0 ? 'text-green-500' : 'text-red-500'}">${analytics.conversionTrend >= 0 ? '+' : ''}${analytics.conversionTrend}% from last period</p>
                                </div>
                                <i class="fas fa-percentage text-orange-500 text-xl"></i>
                            </div>
                        </div>

                        <div class="bg-white dark:bg-gray-700 p-4 rounded-lg border border-gray-200 dark:border-gray-600">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm text-gray-600 dark:text-gray-400">Avg. ROI</p>
                                    <p class="text-2xl font-bold text-gray-900 dark:text-white">$${analytics.avgROI}</p>
                                    <p class="text-xs ${analytics.roiTrend >= 0 ? 'text-green-500' : 'text-red-500'}">${analytics.roiTrend >= 0 ? '+' : ''}${analytics.roiTrend}% from last period</p>
                                </div>
                                <i class="fas fa-dollar-sign text-yellow-500 text-xl"></i>
                            </div>
                        </div>
                    </div>

                    <!-- Tab Navigation -->
                    <div class="flex space-x-2 mb-6">
                        <button id="analytics-tab-history" class="px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium" onclick="showAnalyticsTab('history')">
                            <i class="fas fa-history mr-2"></i>Search History
                        </button>
                        <button id="analytics-tab-popular" class="px-4 py-2 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md text-sm font-medium" onclick="showAnalyticsTab('popular')">
                            <i class="fas fa-fire mr-2"></i>Popular Queries
                        </button>
                        <button id="analytics-tab-clicks" class="px-4 py-2 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md text-sm font-medium" onclick="showAnalyticsTab('clicks')">
                            <i class="fas fa-mouse-pointer mr-2"></i>Click Analytics
                        </button>
                        <button id="analytics-tab-insights" class="px-4 py-2 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md text-sm font-medium" onclick="showAnalyticsTab('insights')">
                            <i class="fas fa-lightbulb mr-2"></i>Insights
                        </button>
                    </div>



                    <!-- Search History Tab Content -->
                    <div id="analytics-history-tab" class="mt-6">
                        <div class="bg-white dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600">
                            <div class="p-4 border-b border-gray-200 dark:border-gray-600">
                                <div class="flex items-center justify-between">
                                    <h5 class="font-medium text-gray-900 dark:text-white">Recent Search History</h5>
                                    <div class="flex space-x-2">
                                        <button onclick="selectAllSearches()" class="px-3 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded text-sm">
                                            <i class="fas fa-check-square mr-1"></i>Select All
                                        </button>
                                        <button onclick="deleteSelectedSearches()" class="px-3 py-1 bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 rounded text-sm">
                                            <i class="fas fa-trash mr-1"></i>Delete Selected
                                        </button>
                                        <button onclick="exportSearchHistory()" class="px-3 py-1 bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 rounded text-sm">
                                            <i class="fas fa-download mr-1"></i>Export History
                                        </button>
                                        <button onclick="clearAllSearchHistory()" class="px-3 py-1 bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 rounded text-sm">
                                            <i class="fas fa-times mr-1"></i>Clear All
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="max-h-96 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600 scrollbar-track-gray-100 dark:scrollbar-track-gray-700">
                                ${recentSearches.map((search, index) => `
                                    <div class="p-4 border-b border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors">
                                        <div class="flex items-start justify-between">
                                            <div class="flex items-start flex-1">
                                                <input type="checkbox" class="mt-1 mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" data-search-index="${index}">
                                                <div class="flex-1 min-w-0">
                                                    <p class="text-sm font-medium text-gray-900 dark:text-white leading-tight mb-1">${search.query || `${search.name || search.field || 'No field'} ${search.jobTitle || 'No title'}`}</p>
                                                    <p class="text-xs text-gray-500 dark:text-gray-400">${new Date(search.timestamp).toLocaleDateString()}, ${new Date(search.timestamp).toLocaleTimeString()} • ${search.field || 'No field'} • ${search.jobTitle || 'No title'}</p>
                                                </div>
                                            </div>
                                            <div class="flex space-x-1 ml-4 flex-shrink-0">
                                                <button onclick="loadSearchFromHistoryByIndex(${index})" class="px-2 py-1 bg-blue-500 hover:bg-blue-600 text-white rounded text-xs font-medium transition-colors">
                                                    <i class="fas fa-upload mr-1"></i>Load
                                                </button>
                                                <button onclick="rerunSearch(${index})" class="px-2 py-1 bg-green-500 hover:bg-green-600 text-white rounded text-xs font-medium transition-colors">
                                                    <i class="fas fa-play mr-1"></i>Run
                                                </button>
                                                <button onclick="deleteSearchFromHistory(${index})" class="px-2 py-1 bg-red-500 hover:bg-red-600 text-white rounded text-xs font-medium transition-colors">
                                                    <i class="fas fa-trash mr-1"></i>Delete
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                `).join('')}
                                ${recentSearches.length === 0 ? '<p class="text-gray-500 dark:text-gray-400 text-center py-8">No search history available</p>' : ''}
                            </div>
                        </div>
                    </div>

                    <!-- Popular Queries Tab Content -->
                    <div id="analytics-popular-tab" class="mt-6 hidden">
                        <div class="bg-white dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600">
                            <div class="p-4 border-b border-gray-200 dark:border-gray-600">
                                <h5 class="font-medium text-gray-900 dark:text-white">Most Popular Search Queries</h5>
                            </div>
                            <div class="p-4 space-y-3">
                                ${getPopularQueries().map((query, index) => `
                                    <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-600 rounded-lg">
                                        <div class="flex items-center">
                                            <span class="w-8 h-8 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full flex items-center justify-center text-sm font-medium mr-3">${index + 1}</span>
                                            <div>
                                                <p class="font-medium text-gray-900 dark:text-white">${query.text}</p>
                                                <p class="text-sm text-gray-600 dark:text-gray-400">Used ${query.count} times • ${query.successRate}% success rate</p>
                                            </div>
                                        </div>
                                        <div class="flex space-x-2">
                                            <button onclick="applyPopularQuery('${query.text}')" class="px-3 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded text-sm">
                                                Use Query
                                            </button>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    </div>

                    <!-- Click Analytics Tab Content -->
                    <div id="analytics-clicks-tab" class="mt-6 hidden">
                        <div class="bg-white dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600">
                            <div class="p-4 border-b border-gray-200 dark:border-gray-600">
                                <h5 class="font-medium text-gray-900 dark:text-white">Click Analytics & Performance</h5>
                            </div>
                            <div class="p-4">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                                    <div class="bg-blue-50 dark:bg-blue-900 p-4 rounded-lg">
                                        <h6 class="font-medium text-blue-800 dark:text-blue-200 mb-2">Total Profile Clicks</h6>
                                        <p class="text-2xl font-bold text-blue-900 dark:text-blue-100">${analytics.profileClicks}</p>
                                        <p class="text-sm text-blue-600 dark:text-blue-300">+12% from last week</p>
                                    </div>
                                    <div class="bg-green-50 dark:bg-green-900 p-4 rounded-lg">
                                        <h6 class="font-medium text-green-800 dark:text-green-200 mb-2">Click-through Rate</h6>
                                        <p class="text-2xl font-bold text-green-900 dark:text-green-100">${analytics.clickThroughRate}%</p>
                                        <p class="text-sm text-green-600 dark:text-green-300">+5% from last week</p>
                                    </div>
                                </div>
                                <div class="space-y-3">
                                    ${getClickAnalytics().map(click => `
                                        <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-600 rounded-lg">
                                            <div>
                                                <p class="font-medium text-gray-900 dark:text-white">${click.profile}</p>
                                                <p class="text-sm text-gray-600 dark:text-gray-400">${click.timestamp} • ${click.source}</p>
                                            </div>
                                            <span class="px-2 py-1 bg-${click.status === 'successful' ? 'green' : 'yellow'}-100 dark:bg-${click.status === 'successful' ? 'green' : 'yellow'}-900 text-${click.status === 'successful' ? 'green' : 'yellow'}-800 dark:text-${click.status === 'successful' ? 'green' : 'yellow'}-200 text-xs rounded">${click.status}</span>
                                        </div>
                                    `).join('')}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Insights Tab Content -->
                    <div id="analytics-insights-tab" class="mt-6 hidden">
                        <div class="bg-white dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600">
                            <div class="p-4 border-b border-gray-200 dark:border-gray-600">
                                <h5 class="font-medium text-gray-900 dark:text-white">AI-Powered Insights & Recommendations</h5>
                            </div>
                            <div class="p-4 space-y-4">
                                ${getAnalyticsInsights().map(insight => `
                                    <div class="bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900 dark:to-blue-900 p-4 rounded-lg border border-purple-200 dark:border-purple-700">
                                        <div class="flex items-start">
                                            <i class="fas fa-${insight.icon} text-purple-600 dark:text-purple-400 mr-3 mt-1"></i>
                                            <div class="flex-1">
                                                <h6 class="font-medium text-gray-900 dark:text-white mb-2">${insight.title}</h6>
                                                <p class="text-sm text-gray-600 dark:text-gray-300 mb-3">${insight.description}</p>
                                                <div class="flex items-center justify-between">
                                                    <span class="px-2 py-1 bg-purple-100 dark:bg-purple-800 text-purple-800 dark:text-purple-200 text-xs rounded">${insight.impact}</span>
                                                    <button onclick="applyInsight('${insight.action}')" class="text-sm text-purple-600 dark:text-purple-400 hover:underline">${insight.actionText}</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Update the analytics container content
            analyticsContainer.innerHTML = analyticsContent;
        }

        function hideAnalyticsDashboard() {
            const analyticsContainer = document.getElementById('analytics-dashboard');
            if (analyticsContainer) {
                analyticsContainer.style.display = 'none';
            }
        }

        // Analytics utility functions
        function refreshAnalytics() {
            showAnalyticsModal(); // Refresh the dashboard
            showSuccess('Analytics refreshed!');
        }

        function exportAnalytics() {
            const searchHistory = JSON.parse(localStorage.getItem('searchHistory') || '[]');
            const analytics = calculateAnalytics(searchHistory);

            const csvContent = `Date,Total Searches,Success Rate,Profile Clicks,Conversion Rate,ROI\n${new Date().toLocaleDateString()},${analytics.totalSearches},${analytics.successRate}%,${analytics.profileClicks},${analytics.conversionRate}%,$${analytics.avgROI}`;

            const blob = new Blob([csvContent], { type: 'text/csv' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `analytics-${new Date().toISOString().split('T')[0]}.csv`;
            a.click();
            window.URL.revokeObjectURL(url);

            showSuccess('Analytics exported successfully!');
        }

        function selectAllSearches() {
            const checkboxes = document.querySelectorAll('input[data-search-index]');
            checkboxes.forEach(cb => cb.checked = true);
        }

        function deleteSelectedSearches() {
            const checkboxes = document.querySelectorAll('input[data-search-index]:checked');
            const indices = Array.from(checkboxes).map(cb => parseInt(cb.dataset.searchIndex));

            if (indices.length === 0) {
                alert('Please select searches to delete');
                return;
            }

            if (confirm(`Delete ${indices.length} selected search(es)?`)) {
                let searchHistory = JSON.parse(localStorage.getItem('searchHistory') || '[]');
                // Remove in reverse order to maintain indices
                indices.sort((a, b) => b - a).forEach(index => {
                    searchHistory.splice(index, 1);
                });
                localStorage.setItem('facebookSearchHistory', JSON.stringify(searchHistory));
                showAnalyticsModal(); // Refresh
                showSuccess(`${indices.length} search(es) deleted`);
            }
        }

        function exportSearchHistory() {
            const searchHistory = JSON.parse(localStorage.getItem('facebookSearchHistory') || '[]');
            if (searchHistory.length === 0) {
                alert('No search history to export');
                return;
            }

            const csvContent = 'Date,Time,Search Type,Field,Job Title,Location,Search Engine,Query\n' +
                searchHistory.map(search =>
                    `"${new Date(search.timestamp).toLocaleDateString()}","${new Date(search.timestamp).toLocaleTimeString()}","${search.type || ''}","${search.field || ''}","${search.jobTitle || ''}","${search.location || ''}","${search.searchEngine || 'google'}","${search.query || ''}"`
                ).join('\n');

            const blob = new Blob([csvContent], { type: 'text/csv' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `search-history-${new Date().toISOString().split('T')[0]}.csv`;
            a.click();
            window.URL.revokeObjectURL(url);

            showSuccess('Search history exported successfully!');
        }

        function clearAllSearchHistory() {
            if (confirm('Are you sure you want to clear all search history? This cannot be undone.')) {
                localStorage.removeItem('searchHistory');
                showAnalyticsModal(); // Refresh
                showSuccess('Search history cleared');
            }
        }

        function rerunSearch(index) {
            const searchHistory = JSON.parse(localStorage.getItem('searchHistory') || '[]');
            const search = searchHistory[index];
            if (search && search.query) {
                window.open(search.query, '_blank');
                showSuccess('Search rerun in new tab');
            }
        }

        function loadSearchFromHistoryByIndex(index) {
            const searchHistory = JSON.parse(localStorage.getItem('searchHistory') || '[]');
            const search = searchHistory[index];
            if (search) {
                // Load the search data into the form
                if (search.type) document.getElementById('searchType').value = search.type;
                if (search.field) document.getElementById('field').value = search.field;
                if (search.jobTitle) document.getElementById('jobTitle').value = search.jobTitle;
                if (search.location) document.getElementById('location').value = search.location;

                showSuccess('Search loaded from history');
            }
        }

        function saveSearchFromHistory(index) {
            const searchHistory = JSON.parse(localStorage.getItem('searchHistory') || '[]');
            const search = searchHistory[index];
            if (search) {
                const savedSearches = JSON.parse(localStorage.getItem('savedSearches') || '[]');
                const savedSearch = {
                    ...search,
                    id: Date.now(),
                    name: `${search.type || 'Search'} - ${search.field || 'No field'} ${search.jobTitle || ''}`.trim(),
                    savedAt: new Date().toISOString()
                };
                savedSearches.push(savedSearch);
                localStorage.setItem('savedSearches', JSON.stringify(savedSearches));
                showSuccess('Search saved successfully');
            }
        }

        function deleteSearchFromHistory(index) {
            if (confirm('Are you sure you want to delete this search from history?')) {
                const searchHistory = JSON.parse(localStorage.getItem('facebookSearchHistory') || '[]');
                if (index >= 0 && index < searchHistory.length) {
                    searchHistory.splice(index, 1);
                    localStorage.setItem('facebookSearchHistory', JSON.stringify(searchHistory));
                    showSuccess('Search deleted from history');
                    // Refresh the analytics modal to show updated history
                    showAnalyticsModal();
                }
            }
        }

        function showFavoritesModal() {
            // Toggle the Favorite Searches section instead of showing a modal
            const favoritesSection = document.getElementById('favoriteSearchesSection');
            const favoritesButton = document.getElementById('toggleFavorites');

            if (favoritesSection) {
                const isHidden = favoritesSection.classList.contains('hidden');

                if (isHidden) {
                    // Show the section
                    favoritesSection.classList.remove('hidden');
                    if (favoritesButton) favoritesButton.classList.add('active');
                } else {
                    // Hide the section
                    favoritesSection.classList.add('hidden');
                    if (favoritesButton) favoritesButton.classList.remove('active');
                }
            }
        }

        // Analytics Helper Functions
        function getSearchCount() {
            const history = loadSearchHistory();
            return history.length;
        }

        function getFavoritesCount() {
            const favorites = loadFavorites();
            return favorites.length;
        }

        function getSavedSearchesCount() {
            const saved = loadSavedSearches();
            return saved.length;
        }

        function getRecentActivity() {
            const history = loadSearchHistory();
            const recent = history.slice(-5).reverse();

            if (recent.length === 0) {
                return '<p class="text-gray-500 dark:text-gray-400">No recent activity</p>';
            }

            return recent.map(item => `
                <div class="flex items-center justify-between py-2 border-b border-gray-200 dark:border-gray-600 last:border-b-0">
                    <div>
                        <p class="font-medium text-gray-800 dark:text-gray-200">${item.searchType || 'Search'}</p>
                        <p class="text-sm text-gray-500 dark:text-gray-400">${item.field || item.name || 'No details'}</p>
                    </div>
                    <span class="text-xs text-gray-400">${new Date(item.timestamp).toLocaleDateString()}</span>
                </div>
            `).join('');
        }

        // Favorites Functions
        function loadFavorites() {
            try {
                const saved = localStorage.getItem('facebookSearchFavorites');
                return saved ? JSON.parse(saved) : [];
            } catch (e) {
                console.error('Error loading favorites:', e);
                return [];
            }
        }

        function saveFavorites(favorites) {
            try {
                localStorage.setItem('facebookSearchFavorites', JSON.stringify(favorites));
            } catch (e) {
                console.error('Error saving favorites:', e);
            }
        }

        function addToFavorites(searchData) {
            const favorites = loadFavorites();
            const newFavorite = {
                ...searchData,
                timestamp: new Date().toISOString(),
                id: Date.now()
            };
            favorites.push(newFavorite);
            saveFavorites(favorites);
            updateFavoritesDisplay();
            showSuccess('Added to favorites!');
        }

        function removeFromFavorites(favoriteId) {
            const favorites = loadFavorites();
            const updatedFavorites = favorites.filter(f => f.id !== favoriteId);
            saveFavorites(updatedFavorites);
            updateFavoritesDisplay();
            showSuccess('Removed from favorites!');
        }

        function loadFavoriteById(favoriteId) {
            const favorites = loadFavorites();
            const favorite = favorites.find(f => f.id === favoriteId);
            if (favorite) {
                // Load form field values
                if (favorite.searchType) document.getElementById('searchType').value = favorite.searchType;
                if (favorite.field) document.getElementById('field').value = favorite.field;
                if (favorite.name) document.getElementById('name').value = favorite.name;
                if (favorite.jobTitle) document.getElementById('jobTitle').value = favorite.jobTitle;
                if (favorite.emailProvider) document.getElementById('emailProvider').value = favorite.emailProvider;
                if (favorite.country) document.getElementById('country').value = favorite.country;
                if (favorite.state) document.getElementById('state').value = favorite.state;
                if (favorite.city) document.getElementById('city').value = favorite.city;

                // Load settings if available
                if (favorite.excludeTerms) {
                    excludeTerms = [...favorite.excludeTerms];
                    updateExcludeDisplay();
                    updateExcludeCounter();
                }

                // Update AI suggestions based on loaded data
                updateAISuggestions();

                showSuccess('Favorite loaded successfully!');
            }
        }

        function updateFavoritesDisplay() {
            const favorites = loadFavorites();
            const container = document.getElementById('favoritesList');

            if (!container) return;

            if (favorites.length === 0) {
                container.innerHTML = '<div class="text-center text-gray-500 dark:text-gray-400 py-4">No favorites</div>';
                return;
            }

            const favoritesHtml = favorites.map(favorite => `
                <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div class="flex-1">
                        <div class="font-medium text-gray-800 dark:text-gray-200">
                            ${favorite.searchType || 'Search'} - ${favorite.field || favorite.name || 'No details'}
                        </div>
                        <div class="text-sm text-gray-500 dark:text-gray-400">
                            ${new Date(favorite.timestamp).toLocaleString()}
                        </div>
                    </div>
                    <div class="flex space-x-2">
                        <button onclick="loadFavoriteById(${favorite.id})" class="text-xs bg-blue-600 text-white px-2 py-1 rounded hover:bg-blue-700">
                            Load
                        </button>
                        <button onclick="removeFromFavorites(${favorite.id})" class="text-xs bg-red-600 text-white px-2 py-1 rounded hover:bg-red-700">
                            Remove
                        </button>
                    </div>
                </div>
            `).join('');

            container.innerHTML = favoritesHtml;
        }

        function getFavoritesModalContent() {
            const favorites = loadFavorites();

            if (favorites.length === 0) {
                return '<p class="text-center text-gray-500 dark:text-gray-400 py-8">No favorites yet. Add searches to favorites from the search history.</p>';
            }

            return `
                <div class="space-y-3">
                    ${favorites.map(favorite => `
                        <div class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                            <div class="flex-1">
                                <h4 class="font-medium text-gray-800 dark:text-gray-200">
                                    ${favorite.searchType || 'Search'} - ${favorite.field || favorite.name || 'No details'}
                                </h4>
                                <p class="text-sm text-gray-500 dark:text-gray-400">
                                    Added: ${new Date(favorite.timestamp).toLocaleDateString()}
                                </p>
                            </div>
                            <div class="flex space-x-2">
                                <button onclick="loadFavoriteById(${favorite.id}); this.closest('.fixed').remove();" class="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700">
                                    Load
                                </button>
                                <button onclick="removeFromFavorites(${favorite.id}); this.closest('.fixed').querySelector('#favoritesModalContent').innerHTML = getFavoritesModalContent();" class="bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700">
                                    Remove
                                </button>
                            </div>
                        </div>
                    `).join('')}
                </div>
            `;
        }

        // License Functions
        function activateLicense() {
            const licenseKeyInput = document.getElementById('activateLicenseKeyInput');
            const emailInput = document.getElementById('activateEmailInput');

            const licenseKey = licenseKeyInput ? licenseKeyInput.value.trim() : '';
            const email = emailInput ? emailInput.value.trim() : '';
            const hardwareId = generateHardwareId();

            if (!licenseKey) {
                alert('Please enter a license key');
                return;
            }

            if (!email) {
                alert('Please enter your email address');
                return;
            }

            // Show loading state
            const activateBtn = document.querySelector('button[onclick="activateLicense()"]');
            const originalText = activateBtn.innerHTML;
            activateBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Activating...';
            activateBtn.disabled = true;

            // Send activation request
            fetch(window.APP_CONFIG.licenseValidationEndpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=activate&license_key=${encodeURIComponent(licenseKey)}&email=${encodeURIComponent(email)}&hardware_id=${encodeURIComponent(hardwareId)}`
            })
            .then(response => response.json())
            .then(data => {
                activateBtn.innerHTML = originalText;
                activateBtn.disabled = false;

                if (data.success) {
                    // Store license info in localStorage
                    localStorage.setItem('facebookLicenseInfo', JSON.stringify({
                        licenseKey: licenseKey,
                        email: email,
                        hardwareId: hardwareId,
                        type: data.license.type,
                        expires: data.license.expires,
                        maxDevices: data.license.max_devices,
                        currentDevices: data.license.current_devices,
                        customerName: data.license.customer_name,
                        activatedAt: new Date().toISOString()
                    }));

                    alert('License activated successfully!');

                    // Close modal and refresh license status
                    const modal = licenseKeyInput.closest('.fixed');
                    if (modal) modal.remove();

                    // Update license status in header
                    updateLicenseStatus();
                } else {
                    alert('License activation failed: ' + data.error);
                }
            })
            .catch(error => {
                activateBtn.innerHTML = originalText;
                activateBtn.disabled = false;
                alert('License activation failed: Network error');
                console.error('License activation error:', error);
            });
        }

        function exportLicense() {
            const licenseInfo = localStorage.getItem('facebookLicenseInfo');

            if (!licenseInfo) {
                alert('No license found to export');
                return;
            }

            try {
                const license = JSON.parse(licenseInfo);

                // Create the export text format (same as LinkedIn)
                const exportText = `Email: ${license.email}\nLicense Key: ${license.licenseKey}`;

                // Create export modal (matching LinkedIn design)
                const modal = document.createElement('div');
                modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
                modal.innerHTML = `
                    <div class="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Export License for Cross-Browser Use</h3>
                        <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
                            Copy this information to activate your license in another browser:
                        </p>
                        <div class="bg-gray-100 dark:bg-gray-700 p-4 rounded mb-4">
                            <div class="font-mono text-sm text-gray-900 dark:text-white">
                                <div><strong>Email:</strong> ${license.email}</div>
                                <div><strong>License Key:</strong> ${license.licenseKey}</div>
                            </div>
                        </div>
                        <div class="flex gap-3">
                            <button id="copyExportData" class="flex-1 bg-blue-500 hover:bg-blue-600 text-white py-2 rounded flex items-center justify-center">
                                <i class="fas fa-copy mr-2"></i>Copy
                            </button>
                            <button id="closeExportDialog" class="flex-1 bg-gray-500 hover:bg-gray-600 text-white py-2 rounded">
                                Close
                            </button>
                        </div>
                    </div>
                `;

                document.body.appendChild(modal);

                // Copy functionality
                document.getElementById('copyExportData').addEventListener('click', function() {
                    navigator.clipboard.writeText(exportText).then(() => {
                        showSuccessMessage('License information copied! Paste this in the other browser.');
                        modal.remove();
                    }).catch(() => {
                        // Fallback for older browsers
                        const textArea = document.createElement('textarea');
                        textArea.value = exportText;
                        document.body.appendChild(textArea);
                        textArea.select();
                        document.execCommand('copy');
                        document.body.removeChild(textArea);
                        showSuccessMessage('License information copied! Paste this in the other browser.');
                        modal.remove();
                    });
                });

                // Close functionality
                document.getElementById('closeExportDialog').addEventListener('click', function() {
                    modal.remove();
                });

            } catch (error) {
                alert('Failed to export license: ' + error.message);
            }
        }

        function importLicense() {
            // Create import modal like LinkedIn version
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
            modal.innerHTML = `
                <div class="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Import License from Another Browser</h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">Paste the license information you copied from the other browser:</p>
                    <textarea
                        id="licenseImportData"
                        placeholder="Paste license information here..."
                        class="w-full h-32 p-3 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white resize-none"
                    ></textarea>
                    <div class="flex gap-3 mt-4">
                        <button onclick="processLicenseImport()" class="flex-1 bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 flex items-center justify-center">
                            <i class="fas fa-download mr-2"></i>Import
                        </button>
                        <button onclick="closeLicenseImportModal()" class="flex-1 bg-gray-500 text-white px-4 py-2 rounded-md hover:bg-gray-600">
                            Cancel
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // Focus on textarea
            setTimeout(() => {
                const textarea = document.getElementById('licenseImportData');
                if (textarea) textarea.focus();
            }, 100);
        }

        // Process the imported license data
        function processLicenseImport() {
            const textarea = document.getElementById('licenseImportData');
            if (!textarea || !textarea.value.trim()) {
                alert('Please paste the license information.');
                return;
            }

            let importData;
            const inputText = textarea.value.trim();

            try {
                // Try to parse as JSON first (old format)
                importData = JSON.parse(inputText);

                // Validate JSON import data
                if (!importData.licenseKey || !importData.email || !importData.application) {
                    alert('Invalid license file format');
                    return;
                }

                if (importData.application !== 'Facebook Graph Search') {
                    alert('This license file is not for Facebook Graph Search');
                    return;
                }
            } catch (e) {
                // If JSON parsing fails, try to parse as text format (new format)
                try {
                    const lines = inputText.split('\n');
                    let email = '';
                    let licenseKey = '';

                    for (const line of lines) {
                        const trimmedLine = line.trim();
                        if (trimmedLine.startsWith('Email:')) {
                            email = trimmedLine.substring(6).trim();
                        } else if (trimmedLine.startsWith('License Key:')) {
                            licenseKey = trimmedLine.substring(12).trim();
                        }
                    }

                    if (!email || !licenseKey) {
                        alert('Invalid license format. Please paste the license information in the correct format.');
                        return;
                    }

                    // Create importData object from parsed text
                    importData = {
                        email: email,
                        licenseKey: licenseKey,
                        application: 'Facebook Graph Search'
                    };
                } catch (textError) {
                    alert('Invalid license format. Please paste the license information in the correct format.');
                    return;
                }
            }

            try {
                // Generate new hardware ID for this browser
                const newHardwareId = generateHardwareId();

                // Show loading state
                const importBtn = document.querySelector('button[onclick="processLicenseImport()"]');
                const originalText = importBtn.innerHTML;
                importBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Importing...';
                importBtn.disabled = true;

                // Activate the imported license
                fetch(window.APP_CONFIG.licenseValidationEndpoint, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `action=activate&license_key=${encodeURIComponent(importData.licenseKey)}&email=${encodeURIComponent(importData.email)}&hardware_id=${encodeURIComponent(newHardwareId)}`
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Store license info
                        localStorage.setItem('facebookLicenseInfo', JSON.stringify({
                            licenseKey: importData.licenseKey,
                            email: importData.email,
                            hardwareId: newHardwareId,
                            type: data.license.type,
                            expires: data.license.expires,
                            maxDevices: data.license.max_devices,
                            currentDevices: data.license.current_devices,
                            customerName: data.license.customer_name,
                            activatedAt: new Date().toISOString()
                        }));

                        alert('License imported and activated successfully!');
                        updateLicenseStatus();
                        updateUIForLicenseTier();

                        // Close modal
                        closeLicenseImportModal();

                    } else {
                        alert('Failed to activate imported license: ' + data.error);
                        // Restore button
                        importBtn.innerHTML = originalText;
                        importBtn.disabled = false;
                    }
                })
                .catch(error => {
                    alert('Failed to activate imported license: Network error');
                    // Restore button
                    importBtn.innerHTML = originalText;
                    importBtn.disabled = false;
                });

            } catch (error) {
                alert('Failed to read license data: ' + error.message);
            }
        }

        // Close the import modal
        function closeLicenseImportModal() {
            const modal = document.querySelector('.fixed.inset-0.bg-black.bg-opacity-50');
            if (modal) {
                modal.remove();
            }
        }

        function upgradeLicense() {
            const upgradeInput = document.getElementById('upgradeLicenseKeyInput');
            const licenseKey = upgradeInput ? upgradeInput.value.trim() : '';

            if (!licenseKey) {
                alert('Please enter a new license key');
                return;
            }

            const currentLicense = localStorage.getItem('facebookLicenseInfo');
            if (!currentLicense) {
                alert('No current license found to upgrade');
                return;
            }

            const license = JSON.parse(currentLicense);
            const hardwareId = generateHardwareId();

            // Show loading state
            const upgradeBtn = document.querySelector('button[onclick="upgradeLicense()"]');
            const originalText = upgradeBtn.innerHTML;
            upgradeBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Upgrading...';
            upgradeBtn.disabled = true;

            // Activate the new license
            fetch(window.APP_CONFIG.licenseValidationEndpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=activate&license_key=${encodeURIComponent(licenseKey)}&email=${encodeURIComponent(license.email)}&hardware_id=${encodeURIComponent(hardwareId)}`
            })
            .then(response => response.json())
            .then(data => {
                upgradeBtn.innerHTML = originalText;
                upgradeBtn.disabled = false;

                if (data.success) {
                    // Update license info
                    localStorage.setItem('facebookLicenseInfo', JSON.stringify({
                        licenseKey: licenseKey,
                        email: license.email,
                        hardwareId: hardwareId,
                        type: data.license.type,
                        expires: data.license.expires,
                        maxDevices: data.license.max_devices,
                        currentDevices: data.license.current_devices,
                        customerName: data.license.customer_name,
                        activatedAt: new Date().toISOString()
                    }));

                    alert(`License upgraded successfully to ${data.license.type}!`);
                    updateLicenseStatus();

                    // Close modal
                    const modal = upgradeInput.closest('.fixed');
                    if (modal) modal.remove();

                } else {
                    alert('License upgrade failed: ' + data.error);
                }
            })
            .catch(error => {
                upgradeBtn.innerHTML = originalText;
                upgradeBtn.disabled = false;
                alert('License upgrade failed: Network error');
            });
        }

        // Hardware ID generation function
        function generateHardwareId() {
            // Try to get existing hardware ID from localStorage
            let hardwareId = localStorage.getItem('facebookHardwareId');

            if (!hardwareId) {
                // Generate a new hardware ID based on browser fingerprint
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                ctx.textBaseline = 'top';
                ctx.font = '14px Arial';
                ctx.fillText('Hardware fingerprint', 2, 2);

                const fingerprint = [
                    navigator.userAgent,
                    navigator.language,
                    screen.width + 'x' + screen.height,
                    new Date().getTimezoneOffset(),
                    canvas.toDataURL()
                ].join('|');

                // Create a simple hash
                let hash = 0;
                for (let i = 0; i < fingerprint.length; i++) {
                    const char = fingerprint.charCodeAt(i);
                    hash = ((hash << 5) - hash) + char;
                    hash = hash & hash; // Convert to 32-bit integer
                }

                // Convert to hex and take first 8 characters
                hardwareId = Math.abs(hash).toString(16).toUpperCase().padStart(8, '0').substring(0, 8);
                localStorage.setItem('facebookHardwareId', hardwareId);
            }

            return hardwareId;
        }

        // License status update function
        function updateLicenseStatus() {
            const licenseInfo = localStorage.getItem('facebookLicenseInfo');
            const statusElement = document.getElementById('licenseText');

            if (licenseInfo && statusElement) {
                const license = JSON.parse(licenseInfo);
                const expiryDate = new Date(license.expires);
                const currentDate = new Date();
                const daysLeft = Math.ceil((expiryDate - currentDate) / (1000 * 60 * 60 * 24));

                if (daysLeft > 0) {
                    const licenseType = license.type.charAt(0).toUpperCase() + license.type.slice(1);
                    statusElement.textContent = `${licenseType}: ${daysLeft} days left`;
                } else {
                    statusElement.textContent = 'License Expired';
                }
            } else {
                // Check for active license on page load
                checkLicenseStatus();
            }

            // Update UI based on license tier
            updateUIForLicenseTier();
        }

        // Update UI elements based on license tier
        function updateUIForLicenseTier() {
            const licenseInfo = localStorage.getItem('facebookLicenseInfo');
            const licenseType = licenseInfo ? JSON.parse(licenseInfo).type : 'none';

            // Update tab buttons with lock indicators
            updateTabButton('bulkSearchTab', 'bulk_search', 'Bulk Search');
            updateTabButton('visualBuilderTab', 'visual_query_builder', 'Visual Query Builder');

            // Update analytics button
            updateAnalyticsButton();

            // Update contact enrichment based on license
            updateContactEnrichment();
        }

        // Update individual tab button with lock indicator
        function updateTabButton(buttonId, requiredFeature, featureName) {
            const button = document.getElementById(buttonId);
            if (!button) return;

            const hasAccess = hasFeature(requiredFeature);
            const icon = button.querySelector('i');
            const text = button.querySelector('span') || button;

            if (hasAccess) {
                // Remove lock indicator if present
                button.classList.remove('opacity-50', 'cursor-not-allowed');
                button.title = '';
                if (text.textContent.includes('🔒')) {
                    text.textContent = text.textContent.replace(' 🔒 License Required', '');
                }
            } else {
                // Add lock indicator
                button.classList.add('opacity-50');
                button.title = `${featureName} requires Professional or Enterprise license`;
                if (!text.textContent.includes('🔒')) {
                    text.textContent += ' 🔒 License Required';
                }
            }
        }

        // Update analytics button
        function updateAnalyticsButton() {
            const button = document.getElementById('toggleAnalytics');
            if (!button) return;

            const hasBasicAnalytics = hasFeature('basic_analytics');
            const hasAdvancedAnalytics = hasFeature('advanced_analytics');

            if (!hasBasicAnalytics) {
                button.classList.add('opacity-50');
                button.title = 'Analytics requires Basic license or higher';
                const text = button.querySelector('span') || button;
                if (!text.textContent.includes('🔒')) {
                    text.textContent += ' 🔒';
                }
            } else {
                button.classList.remove('opacity-50');
                button.title = hasAdvancedAnalytics ? 'Advanced Analytics Available' : 'Basic Analytics Available';
                const text = button.querySelector('span') || button;
                if (text.textContent.includes('🔒')) {
                    text.textContent = text.textContent.replace(' 🔒', '');
                }
            }
        }

        // Update contact enrichment based on license tier
        function updateContactEnrichment() {
            const enrichmentToggle = document.getElementById('enrichmentToggle');
            const enrichmentStatus = document.getElementById('enrichmentStatus');
            const enrichmentSettings = document.getElementById('enrichmentSettings');

            if (!enrichmentToggle || !enrichmentStatus) return;

            const hasBasicEnrichment = hasFeature('basic_enrichment');
            const hasFullEnrichment = hasFeature('full_enrichment');
            const hasPremiumEnrichment = hasFeature('premium_enrichment');

            if (!hasBasicEnrichment) {
                // No enrichment access - disable completely
                enrichmentToggle.checked = false;
                enrichmentToggle.disabled = true;
                enrichmentStatus.textContent = 'License Required';
                enrichmentStatus.classList.add('text-red-500');
                if (enrichmentSettings) {
                    enrichmentSettings.classList.add('opacity-50', 'cursor-not-allowed');
                    enrichmentSettings.title = 'Contact enrichment requires Basic license or higher';
                }
            } else {
                // Has some level of enrichment access
                enrichmentToggle.disabled = false;
                enrichmentStatus.classList.remove('text-red-500');

                // Load saved preference or default to enabled
                const savedPreference = localStorage.getItem('contactEnrichmentEnabled');
                const isEnabled = savedPreference !== null ? savedPreference === 'true' : true;

                enrichmentToggle.checked = isEnabled;
                enrichmentStatus.textContent = isEnabled ? 'Enabled' : 'Disabled';

                if (enrichmentSettings) {
                    enrichmentSettings.classList.remove('opacity-50', 'cursor-not-allowed');

                    // Update settings button title based on license tier
                    if (hasPremiumEnrichment) {
                        enrichmentSettings.title = 'Premium enrichment settings (all 9 platforms)';
                    } else if (hasFullEnrichment) {
                        enrichmentSettings.title = 'Full enrichment settings (6 platforms)';
                    } else {
                        enrichmentSettings.title = 'Basic enrichment settings (3 platforms)';
                    }
                }
            }
        }

        // Check license status function
        function checkLicenseStatus() {
            const hardwareId = generateHardwareId();

            fetch(`${window.APP_CONFIG.licenseValidationEndpoint}?action=check&hardware_id=${encodeURIComponent(hardwareId)}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Check if license is expired
                    const expiryDate = new Date(data.license.expires);
                    const currentDate = new Date();

                    if (expiryDate < currentDate) {
                        // License is expired - block all functionality
                        localStorage.removeItem('facebookLicenseInfo');
                        showLicenseExpiredModal();
                        return;
                    }

                    // Store license info
                    localStorage.setItem('facebookLicenseInfo', JSON.stringify({
                        licenseKey: data.license.license_key,
                        type: data.license.type,
                        expires: data.license.expires,
                        maxDevices: data.license.max_devices,
                        currentDevices: data.license.current_devices,
                        customerName: data.license.customer_name,
                        hardwareId: hardwareId
                    }));

                    updateLicenseStatus();
                } else {
                    // No active license found, keep trial status
                    console.log('No active license found');
                }
            })
            .catch(error => {
                console.error('License status check failed:', error);
            });
        }

        // Check if user has valid license before allowing actions
        function checkLicenseBeforeAction(actionName = 'this feature') {
            const licenseInfo = localStorage.getItem('facebookLicenseInfo');

            if (!licenseInfo) {
                showLicenseRequiredModal(actionName);
                return false;
            }

            const license = JSON.parse(licenseInfo);
            const expiryDate = new Date(license.expires);
            const currentDate = new Date();

            if (expiryDate < currentDate) {
                localStorage.removeItem('facebookLicenseInfo');
                showLicenseExpiredModal();
                return false;
            }

            return true;
        }

        // Check if user has specific feature access based on license tier
        function hasFeature(featureName) {
            const licenseInfo = localStorage.getItem('facebookLicenseInfo');

            if (!licenseInfo) {
                return false;
            }

            const license = JSON.parse(licenseInfo);
            const licenseType = license.type.toLowerCase();

            // Define feature access by license tier
            const featureMatrix = {
                'basic': [
                    'basic_search',
                    'basic_analytics',
                    'export_csv',
                    'search_history',
                    'basic_enrichment'
                ],
                'professional': [
                    'basic_search',
                    'basic_analytics',
                    'export_csv',
                    'search_history',
                    'basic_enrichment',
                    'bulk_search',
                    'advanced_analytics',
                    'export_excel',
                    'export_pdf',
                    'visual_query_builder',
                    'auto_complete',
                    'full_enrichment',
                    'email_patterns',
                    'company_data'
                ],
                'enterprise': [
                    'basic_search',
                    'basic_analytics',
                    'export_csv',
                    'search_history',
                    'basic_enrichment',
                    'bulk_search',
                    'advanced_analytics',
                    'export_excel',
                    'export_pdf',
                    'visual_query_builder',
                    'auto_complete',
                    'full_enrichment',
                    'email_patterns',
                    'company_data',
                    'premium_enrichment',
                    'advanced_enrichment_settings',
                    'custom_enrichment',
                    'white_label',
                    'api_access',
                    'priority_support',
                    'custom_integrations',
                    'team_management'
                ]
            };

            const allowedFeatures = featureMatrix[licenseType] || [];
            return allowedFeatures.includes(featureName);
        }

        // Check license tier and show upgrade modal if needed
        function checkLicenseTier(requiredFeature, actionName = 'this feature') {
            if (!checkLicenseBeforeAction(actionName)) {
                return false;
            }

            if (!hasFeature(requiredFeature)) {
                showUpgradeRequiredModal(requiredFeature, actionName);
                return false;
            }

            return true;
        }

        // Show license expired modal
        function showLicenseExpiredModal() {
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
            modal.innerHTML = `
                <div class="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md mx-4 text-center">
                    <div class="w-16 h-16 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-exclamation-triangle text-red-600 dark:text-red-400 text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2">License Expired</h3>
                    <p class="text-gray-600 dark:text-gray-400 mb-6">
                        Your license has expired. Please renew your license to continue using the application.
                    </p>
                    <div class="flex gap-3">
                        <button onclick="this.closest('.fixed').remove()" class="flex-1 bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg">
                            Close
                        </button>
                        <button onclick="showLicenseModal(); this.closest('.fixed').remove()" class="flex-1 bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg">
                            Renew License
                        </button>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        }

        // Show license required modal
        function showLicenseRequiredModal(actionName) {
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
            modal.innerHTML = `
                <div class="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md mx-4 text-center">
                    <div class="w-16 h-16 bg-yellow-100 dark:bg-yellow-900 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-key text-yellow-600 dark:text-yellow-400 text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2">License Required</h3>
                    <p class="text-gray-600 dark:text-gray-400 mb-6">
                        You need an active license to use ${actionName}. Please activate a license to continue.
                    </p>
                    <div class="flex gap-3">
                        <button onclick="this.closest('.fixed').remove()" class="flex-1 bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg">
                            Close
                        </button>
                        <button onclick="showLicenseModal(); this.closest('.fixed').remove()" class="flex-1 bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg">
                            Activate License
                        </button>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        }

        // Show upgrade required modal
        function showUpgradeRequiredModal(requiredFeature, actionName) {
            const licenseInfo = localStorage.getItem('facebookLicenseInfo');
            const currentTier = licenseInfo ? JSON.parse(licenseInfo).type : 'none';

            let requiredTier = 'Professional';
            if (['premium_enrichment', 'white_label', 'api_access', 'team_management'].includes(requiredFeature)) {
                requiredTier = 'Enterprise';
            }

            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
            modal.innerHTML = `
                <div class="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md mx-4 text-center">
                    <div class="w-16 h-16 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-arrow-up text-purple-600 dark:text-purple-400 text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2">Upgrade Required</h3>
                    <p class="text-gray-600 dark:text-gray-400 mb-2">
                        ${actionName} requires a ${requiredTier} license.
                    </p>
                    <p class="text-sm text-gray-500 dark:text-gray-400 mb-6">
                        Current: ${currentTier.charAt(0).toUpperCase() + currentTier.slice(1)} → Required: ${requiredTier}
                    </p>
                    <div class="flex gap-3">
                        <button onclick="this.closest('.fixed').remove()" class="flex-1 bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg">
                            Close
                        </button>
                        <button onclick="showLicenseModal(); this.closest('.fixed').remove()" class="flex-1 bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg">
                            Upgrade License
                        </button>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        }

        function showAnalyticsTab(tabName) {
            // Reset all tab buttons
            const tabs = ['history', 'popular', 'clicks', 'insights'];
            tabs.forEach(tab => {
                const button = document.getElementById(`analytics-tab-${tab}`);
                const content = document.getElementById(`analytics-${tab}-tab`);

                if (button) {
                    button.className = 'px-4 py-2 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md text-sm font-medium';
                }

                if (content) {
                    content.classList.add('hidden');
                }
            });

            // Activate selected tab
            const activeButton = document.getElementById(`analytics-tab-${tabName}`);
            const activeContent = document.getElementById(`analytics-${tabName}-tab`);

            if (activeButton) {
                activeButton.className = 'px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium';
            }

            if (activeContent) {
                activeContent.classList.remove('hidden');
            }

            console.log(`Switched to ${tabName} tab`);
        }

        // Helper functions for analytics tab content
        function getPopularQueries() {
            const searchHistory = JSON.parse(localStorage.getItem('searchHistory') || '[]');
            const queryCount = {};

            searchHistory.forEach(search => {
                const query = search.query || `${search.type} search`;
                queryCount[query] = (queryCount[query] || 0) + 1;
            });

            return Object.entries(queryCount)
                .map(([text, count]) => ({
                    text,
                    count,
                    successRate: Math.floor(Math.random() * 30) + 70 // Mock success rate
                }))
                .sort((a, b) => b.count - a.count)
                .slice(0, 10);
        }

        function getClickAnalytics() {
            return [
                { profile: 'John Smith - Marketing Manager', timestamp: '2 hours ago', source: 'LinkedIn', status: 'successful' },
                { profile: 'Sarah Johnson - Product Director', timestamp: '4 hours ago', source: 'Google', status: 'successful' },
                { profile: 'Michael Brown - Sales Lead', timestamp: '6 hours ago', source: 'LinkedIn', status: 'pending' },
                { profile: 'Emily Davis - UX Designer', timestamp: '1 day ago', source: 'Google', status: 'successful' },
                { profile: 'David Wilson - Software Engineer', timestamp: '1 day ago', source: 'LinkedIn', status: 'successful' }
            ];
        }

        function getAnalyticsInsights() {
            return [
                {
                    icon: 'lightbulb',
                    title: 'Optimize Search Timing',
                    description: 'Your searches perform 23% better when conducted between 9-11 AM on weekdays.',
                    impact: 'High Impact',
                    action: 'schedule_searches',
                    actionText: 'Set Reminders'
                },
                {
                    icon: 'target',
                    title: 'Refine Location Targeting',
                    description: 'Adding specific cities to your searches increases success rate by 18%.',
                    impact: 'Medium Impact',
                    action: 'add_location',
                    actionText: 'Apply Now'
                },
                {
                    icon: 'chart-line',
                    title: 'Trending Keywords',
                    description: 'Including "remote" or "hybrid" in job titles shows 31% higher engagement.',
                    impact: 'High Impact',
                    action: 'add_keywords',
                    actionText: 'Update Searches'
                }
            ];
        }

        function applyPopularQuery(query) {
            // Parse the query and apply to form fields
            showAISuggestionMessage(`Applied popular query: ${query}`);
        }

        function applyInsight(action) {
            switch(action) {
                case 'schedule_searches':
                    showAISuggestionMessage('Search scheduling feature coming soon!');
                    break;
                case 'add_location':
                    document.getElementById('city').focus();
                    showAISuggestionMessage('Focus on city field - add specific location for better results');
                    break;
                case 'add_keywords':
                    const jobTitleInput = document.getElementById('jobTitle');
                    if (jobTitleInput && !jobTitleInput.value.includes('remote')) {
                        jobTitleInput.value += ' remote';
                        showAISuggestionMessage('Added "remote" keyword to job title');
                    }
                    break;
                default:
                    showAISuggestionMessage('Insight applied successfully!');
            }
        }

        // Make functions globally accessible
        window.activateLicense = activateLicense;
        window.exportLicense = exportLicense;
        window.importLicense = importLicense;
        window.upgradeLicense = upgradeLicense;
        window.generateHardwareId = generateHardwareId;
        window.updateLicenseStatus = updateLicenseStatus;
        window.checkLicenseStatus = checkLicenseStatus;
        window.showAnalyticsTab = showAnalyticsTab;
        window.refreshAnalytics = refreshAnalytics;
        window.exportAnalytics = exportAnalytics;
        window.selectAllSearches = selectAllSearches;
        window.deleteSelectedSearches = deleteSelectedSearches;
        window.exportSearchHistory = exportSearchHistory;
        window.clearAllSearchHistory = clearAllSearchHistory;
        window.rerunSearch = rerunSearch;
        window.loadSearchFromHistoryByIndex = loadSearchFromHistoryByIndex;
        window.saveSearchFromHistory = saveSearchFromHistory;
        window.addToFavorites = addToFavorites;
        window.removeFromFavorites = removeFromFavorites;
        window.loadFavoriteById = loadFavoriteById;
        window.getFavoritesModalContent = getFavoritesModalContent;
        window.selectSuggestion = selectSuggestion;
        window.applyPopularQuery = applyPopularQuery;
        window.applyInsight = applyInsight;
        window.copyToClipboard = copyToClipboard;
        window.searchOnFacebook = searchOnFacebook;
        window.openFacebookSearch = openFacebookSearch;
        window.downloadQueries = downloadQueries;
        window.copyAllQueries = copyAllQueries;
        window.loadConfiguration = loadConfiguration;
        window.duplicateConfiguration = duplicateConfiguration;
        window.deleteConfiguration = deleteConfiguration;

        // Helper functions for bulk queries
        function copyToClipboard(text, button) {
            navigator.clipboard.writeText(text).then(() => {
                const originalText = button.innerHTML;
                button.innerHTML = '<i class="fas fa-check mr-1"></i>Copied!';
                button.classList.add('text-green-600');
                setTimeout(() => {
                    button.innerHTML = originalText;
                    button.classList.remove('text-green-600');
                }, 2000);
            });
        }

        function searchOnFacebook(query, button) {
            // Encode the query for URL
            const encodedQuery = encodeURIComponent(query);
            const facebookSearchUrl = `${window.APP_CONFIG.facebookSearchBaseUrl}people/?q=${encodedQuery}`;

            // Open Facebook search in new tab
            window.open(facebookSearchUrl, '_blank');

            // Visual feedback
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-external-link-alt mr-1"></i>Opened!';
            button.classList.add('text-green-600');
            setTimeout(() => {
                button.innerHTML = originalText;
                button.classList.remove('text-green-600');
            }, 2000);
        }

        function openFacebookSearch(url, button) {
            // Open the pre-built Facebook search URL in new tab
            window.open(url, '_blank');

            // Visual feedback
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-external-link-alt mr-1"></i>Opened!';
            button.classList.add('text-green-600');
            setTimeout(() => {
                button.innerHTML = originalText;
                button.classList.remove('text-green-600');
            }, 2000);
        }

        function downloadQueries(queries) {
            const content = queries.join('\n');
            const blob = new Blob([content], { type: 'text/plain' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `facebook-bulk-queries-${new Date().toISOString().split('T')[0]}.txt`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
            showAISuggestionMessage('Queries downloaded successfully!');
        }

        function copyAllQueries(queries) {
            const content = queries.join('\n');
            navigator.clipboard.writeText(content).then(() => {
                showAISuggestionMessage(`Copied all ${queries.length} queries to clipboard!`);
            });
        }

        // Show error message function
        function showError(message) {
            // Create or update message element
            let messageEl = document.getElementById('globalMessages');
            if (!messageEl) {
                messageEl = document.createElement('div');
                messageEl.id = 'globalMessages';
                messageEl.className = 'mb-4';
                document.querySelector('.container').insertBefore(messageEl, document.querySelector('.container').firstChild);
            }

            messageEl.innerHTML = `
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                    <span class="block sm:inline">${message}</span>
                </div>
            `;

            // Auto-hide after 3 seconds
            setTimeout(() => {
                messageEl.innerHTML = '';
            }, 3000);
        }

        // Aliases for compatibility
        const showSuccess = showSuccessMessage;
        const showInfo = showSuccessMessage;

        // Initialize templates on page load
        populateTemplates();

        // ===== SEARCH SETTINGS FUNCTIONALITY =====

        // Global settings variables for Facebook search
        let facebookSettings = {
            privacyLevel: 'all',
            locationRadius: '',
            timeFilter: '',
            includePhotos: true,
            includeVideos: true,
            includeLinks: true,
            includeCheckins: false
        };
        let searchTemplates = [];
        let excludeTerms = []; // Make excludeTerms globally accessible

        // Load saved settings from localStorage
        function loadSearchSettings() {
            try {
                const savedSettings = localStorage.getItem('facebookSearchSettings');
                if (savedSettings) {
                    const settings = JSON.parse(savedSettings);
                    facebookSettings = { ...facebookSettings, ...settings.facebookSettings };

                    // Apply loaded settings to UI
                    applySettingsToUI();
                }
            } catch (error) {
                console.error('Error loading search settings:', error);
            }
        }

        // Save settings to localStorage
        function saveSearchSettings() {
            try {
                const settings = {
                    facebookSettings,
                    lastUpdated: new Date().toISOString()
                };
                localStorage.setItem('facebookSearchSettings', JSON.stringify(settings));
            } catch (error) {
                console.error('Error saving search settings:', error);
            }
        }

        // Apply settings to UI elements
        function applySettingsToUI() {
            // Apply Facebook settings
            const privacyLevelSelect = document.getElementById('privacyLevel');
            if (privacyLevelSelect) privacyLevelSelect.value = facebookSettings.privacyLevel;

            const locationRadiusSelect = document.getElementById('locationRadius');
            if (locationRadiusSelect) locationRadiusSelect.value = facebookSettings.locationRadius;

            const timeFilterSelect = document.getElementById('timeFilter');
            if (timeFilterSelect) timeFilterSelect.value = facebookSettings.timeFilter;

            // Apply content type checkboxes
            const includePhotosCheckbox = document.getElementById('includePhotos');
            if (includePhotosCheckbox) includePhotosCheckbox.checked = facebookSettings.includePhotos;

            const includeVideosCheckbox = document.getElementById('includeVideos');
            if (includeVideosCheckbox) includeVideosCheckbox.checked = facebookSettings.includeVideos;

            const includeLinksCheckbox = document.getElementById('includeLinks');
            if (includeLinksCheckbox) includeLinksCheckbox.checked = facebookSettings.includeLinks;

            const includeCheckinsCheckbox = document.getElementById('includeCheckins');
            if (includeCheckinsCheckbox) includeCheckinsCheckbox.checked = facebookSettings.includeCheckins;
        }

        // Facebook Settings Event Listeners
        const privacyLevelSelect = document.getElementById('privacyLevel');
        if (privacyLevelSelect) {
            privacyLevelSelect.addEventListener('change', function() {
                facebookSettings.privacyLevel = this.value;
                saveSearchSettings();
                showSuccess(`Privacy level changed to ${this.options[this.selectedIndex].text}`);
            });
        }

        const locationRadiusSelect = document.getElementById('locationRadius');
        if (locationRadiusSelect) {
            locationRadiusSelect.addEventListener('change', function() {
                facebookSettings.locationRadius = this.value;
                saveSearchSettings();
                const radiusText = this.value ? `${this.value} miles` : 'Any distance';
                showSuccess(`Location radius changed to ${radiusText}`);
            });
        }

        const timeFilterSelect = document.getElementById('timeFilter');
        if (timeFilterSelect) {
            timeFilterSelect.addEventListener('change', function() {
                facebookSettings.timeFilter = this.value;
                saveSearchSettings();
                const timeText = this.options[this.selectedIndex].text;
                showSuccess(`Time filter changed to ${timeText}`);
            });
        }

        // Content type checkboxes
        const includePhotosCheckbox = document.getElementById('includePhotos');
        if (includePhotosCheckbox) {
            includePhotosCheckbox.addEventListener('change', function() {
                facebookSettings.includePhotos = this.checked;
                saveSearchSettings();
                showSuccess(`Photos ${this.checked ? 'included' : 'excluded'} in search`);
            });
        }

        const includeVideosCheckbox = document.getElementById('includeVideos');
        if (includeVideosCheckbox) {
            includeVideosCheckbox.addEventListener('change', function() {
                facebookSettings.includeVideos = this.checked;
                saveSearchSettings();
                showSuccess(`Videos ${this.checked ? 'included' : 'excluded'} in search`);
            });
        }

        const includeLinksCheckbox = document.getElementById('includeLinks');
        if (includeLinksCheckbox) {
            includeLinksCheckbox.addEventListener('change', function() {
                facebookSettings.includeLinks = this.checked;
                saveSearchSettings();
                showSuccess(`Links ${this.checked ? 'included' : 'excluded'} in search`);
            });
        }

        const includeCheckinsCheckbox = document.getElementById('includeCheckins');
        if (includeCheckinsCheckbox) {
            includeCheckinsCheckbox.addEventListener('change', function() {
                facebookSettings.includeCheckins = this.checked;
                saveSearchSettings();
                showSuccess(`Check-ins ${this.checked ? 'included' : 'excluded'} in search`);
            });
        }

        // ===== SEARCH TEMPLATES & PRESETS FUNCTIONALITY =====

        // Load saved templates from localStorage
        function loadSearchTemplates() {
            try {
                const saved = localStorage.getItem('facebookSearchTemplates');
                return saved ? JSON.parse(saved) : [];
            } catch (e) {
                console.error('Error loading search templates:', e);
                return [];
            }
        }

        // Save templates to localStorage
        function saveSearchTemplatesData(templates) {
            try {
                localStorage.setItem('facebookSearchTemplates', JSON.stringify(templates));
            } catch (e) {
                console.error('Error saving search templates:', e);
                showError('Failed to save templates');
            }
        }

        // Update templates display
        function updateTemplatesDisplay() {
            const templates = loadSearchTemplates();
            const container = document.getElementById('templatesListSettings');

            if (templates.length === 0) {
                container.innerHTML = '<div class="text-xs text-gray-500 dark:text-gray-400">No templates saved yet</div>';
            } else {
                const templatesHtml = templates.map((template, index) => {
                    const searchType = template.searchType || 'Unknown';
                    const excludeCount = template.excludeTerms ? template.excludeTerms.length : 0;
                    const hasLocation = (template.city || template.state || template.country) ? 'Location' : 'No Location';

                    return `
                    <div class="bg-gray-50 dark:bg-gray-700 rounded p-2 flex justify-between items-center">
                        <div>
                            <div class="font-medium text-sm">${template.name}</div>
                            <div class="text-xs text-gray-500 dark:text-gray-400">
                                FACEBOOK • ${searchType} • ${excludeCount} excludes • ${hasLocation} • ${new Date(template.createdAt).toLocaleDateString()}
                            </div>
                        </div>
                        <div class="flex space-x-1">
                            <button onclick="loadTemplate(${index})" class="text-xs bg-blue-600 text-white px-2 py-1 rounded hover:bg-blue-700">
                                Load
                            </button>
                            <button onclick="deleteTemplate(${index})" class="text-xs bg-red-600 text-white px-2 py-1 rounded hover:bg-red-700">
                                ×
                            </button>
                        </div>
                    </div>
                    `;
                }).join('');
                container.innerHTML = templatesHtml;
            }
        }

        // Save current settings as template
        function saveCurrentAsTemplate() {
            const templateName = prompt('Enter a name for this template:');
            if (!templateName) return;

            // Get current form values
            const searchType = document.getElementById('searchType').value;
            const field = document.getElementById('field').value;
            const name = document.getElementById('name').value;
            const jobTitle = document.getElementById('jobTitle').value;
            const emailProvider = document.getElementById('emailProvider').value;
            const country = document.getElementById('country').value;
            const state = document.getElementById('state').value;
            const city = document.getElementById('city').value;

            const template = {
                name: templateName,
                engine: 'facebook',
                searchType: searchType,
                field: field,
                nameField: name,
                jobTitle: jobTitle,
                emailProvider: emailProvider,
                country: country,
                state: state,
                city: city,
                excludeTerms: [...excludeTerms],
                facebookSettings: { ...facebookSettings },
                createdAt: new Date().toISOString()
            };

            const templates = loadSearchTemplates();
            templates.push(template);
            saveSearchTemplatesData(templates);
            updateTemplatesDisplay();
            showSuccess(`Template "${templateName}" saved successfully!`);
        }

        // Load template
        function loadTemplate(index) {
            const templates = loadSearchTemplates();
            if (index >= 0 && index < templates.length) {
                const template = templates[index];

                // Apply template form values
                if (template.searchType) document.getElementById('searchType').value = template.searchType;
                if (template.field) document.getElementById('field').value = template.field;
                if (template.nameField) document.getElementById('name').value = template.nameField;
                if (template.jobTitle) document.getElementById('jobTitle').value = template.jobTitle;
                if (template.emailProvider) document.getElementById('emailProvider').value = template.emailProvider;
                if (template.country) document.getElementById('country').value = template.country;
                if (template.state) document.getElementById('state').value = template.state;
                if (template.city) document.getElementById('city').value = template.city;

                // Apply template settings
                if (template.facebookSettings) {
                    facebookSettings = { ...facebookSettings, ...template.facebookSettings };
                }
                if (template.excludeTerms) {
                    excludeTerms = [...template.excludeTerms];
                }

                // Update UI
                applySettingsToUI();
                updateExcludeDisplay();
                updateExcludeCounter();

                // Save current settings
                saveSearchSettings();

                showSuccess(`Template "${template.name}" loaded successfully!`);
            }
        }

        // Delete template
        function deleteTemplate(index) {
            const templates = loadSearchTemplates();
            if (index >= 0 && index < templates.length) {
                const templateName = templates[index].name;
                if (confirm(`Are you sure you want to delete the template "${templateName}"?`)) {
                    templates.splice(index, 1);
                    saveSearchTemplatesData(templates);
                    updateTemplatesDisplay();
                    showSuccess(`Template "${templateName}" deleted successfully!`);
                }
            }
        }

        // Clear all templates
        function clearAllTemplates() {
            if (confirm('Are you sure you want to delete all templates? This action cannot be undone.')) {
                saveSearchTemplatesData([]);
                updateTemplatesDisplay();
                showSuccess('All templates cleared successfully!');
            }
        }

        // Export templates
        function exportTemplates() {
            const templates = loadSearchTemplates();
            if (templates.length === 0) {
                showError('No templates to export');
                return;
            }

            const dataStr = JSON.stringify(templates, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });
            const url = URL.createObjectURL(dataBlob);

            const link = document.createElement('a');
            link.href = url;
            link.download = `facebook-search-templates-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);

            showSuccess('Templates exported successfully!');
        }

        // Make functions globally accessible
        window.loadTemplate = loadTemplate;
        window.deleteTemplate = deleteTemplate;

        // Search Templates Event Listeners
        document.getElementById('saveCurrentAsTemplate').addEventListener('click', saveCurrentAsTemplate);
        document.getElementById('clearAllTemplates').addEventListener('click', clearAllTemplates);
        document.getElementById('exportTemplates').addEventListener('click', exportTemplates);

        // Manage Templates Toggle
        document.getElementById('manageTemplatesSettings').addEventListener('click', function() {
            const container = document.getElementById('templatesListSettings');
            if (container.classList.contains('max-h-32')) {
                container.classList.remove('max-h-32');
                container.classList.add('max-h-64');
                this.innerHTML = '<i class="fas fa-compress mr-1"></i>Collapse';
            } else {
                container.classList.remove('max-h-64');
                container.classList.add('max-h-32');
                this.innerHTML = '<i class="fas fa-cog mr-1"></i>Manage';
            }
        });

        // ===== SETTINGS ACTIONS FUNCTIONALITY =====

        // Reset All Settings
        function resetAllSettings() {
            if (confirm('Are you sure you want to reset all settings to default? This will clear all saved preferences.')) {
                // Reset to defaults
                facebookSettings = {
                    privacyLevel: 'all',
                    locationRadius: '',
                    timeFilter: '',
                    includePhotos: true,
                    includeVideos: true,
                    includeLinks: true,
                    includeCheckins: false
                };
                excludeTerms = [];

                // Clear form fields
                document.getElementById('searchType').value = '';
                document.getElementById('field').value = '';
                document.getElementById('name').value = '';
                document.getElementById('jobTitle').value = '';
                document.getElementById('emailProvider').value = '';
                document.getElementById('country').value = '';
                document.getElementById('state').value = '';
                document.getElementById('city').value = '';

                // Clear localStorage
                localStorage.removeItem('facebookSearchSettings');

                // Update UI
                applySettingsToUI();
                updateExcludeDisplay();
                updateExcludeCounter();

                showSuccess('All settings have been reset to default values!');
            }
        }

        // Save as Preset
        function saveAsPreset() {
            const presetName = prompt('Enter a name for this preset:');
            if (!presetName) return;

            // Get current form values
            const searchType = document.getElementById('searchType').value;
            const field = document.getElementById('field').value;
            const name = document.getElementById('name').value;
            const jobTitle = document.getElementById('jobTitle').value;
            const emailProvider = document.getElementById('emailProvider').value;
            const country = document.getElementById('country').value;
            const state = document.getElementById('state').value;
            const city = document.getElementById('city').value;

            const preset = {
                name: presetName,
                engine: 'facebook',
                searchType: searchType,
                field: field,
                nameField: name,
                jobTitle: jobTitle,
                emailProvider: emailProvider,
                country: country,
                state: state,
                city: city,
                excludeTerms: [...excludeTerms],
                facebookSettings: { ...facebookSettings },
                createdAt: new Date().toISOString()
            };

            try {
                // Save as template (same format as other templates)
                const templates = loadSearchTemplates();
                templates.push(preset);
                saveSearchTemplatesData(templates);
                updateTemplatesDisplay();
                showSuccess(`Preset "${presetName}" saved successfully!`);
            } catch (error) {
                console.error('Error saving preset:', error);
                showError('Failed to save preset');
            }
        }

        // Settings Actions Event Listeners
        document.getElementById('resetAllSettings').addEventListener('click', resetAllSettings);
        document.getElementById('saveSettingsPreset').addEventListener('click', saveAsPreset);

        // Initialize all settings functionality
        function initializeSearchSettings() {
            loadSearchSettings();
            updateTemplatesDisplay();
            // updateExcludeCounter will be called after DOMContentLoaded
            updateSearchHistoryDisplay();
            updateSavedSearchesDisplay();
        }

        // Initialize settings when page loads
        initializeSearchSettings();

        // Tab switching functionality
        document.getElementById('basicSearchTab').addEventListener('click', function() {
            switchTab('basic');
        });

        document.getElementById('bulkSearchTab').addEventListener('click', function() {
            // Check license tier for bulk search (requires Professional or Enterprise)
            if (!checkLicenseTier('bulk_search', 'bulk search functionality')) {
                return;
            }
            switchTab('bulk');
        });

        document.getElementById('visualBuilderTab').addEventListener('click', function() {
            // Check license tier for visual query builder (requires Professional or Enterprise)
            if (!checkLicenseTier('visual_query_builder', 'visual query builder')) {
                return;
            }
            switchTab('visualBuilder');
        });

        function switchTab(tabName) {
            // Remove active class from all tabs (both modern-tab and linkedin-tab)
            document.querySelectorAll('.modern-tab, .linkedin-tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.add('hidden');
            });

            // Activate selected tab and content
            let tabId, contentId;
            if (tabName === 'visualBuilder') {
                tabId = 'visualBuilderTab';
                contentId = 'visualBuilderSearchContent';
            } else {
                tabId = tabName + 'SearchTab';
                contentId = tabName + 'SearchContent';
            }

            const tab = document.getElementById(tabId);
            const content = document.getElementById(contentId);

            if (tab) tab.classList.add('active');
            if (content) content.classList.remove('hidden');

            // Handle Quick Templates visibility based on tab
            const templatesSection = document.querySelector('.mb-6:has(#toggleTemplates)');
            const searchHistorySection = document.getElementById('searchHistorySection');
            const aiSuggestionsSection = document.getElementById('aiSuggestionsSection');

            if (tabName === 'bulk' || tabName === 'visualBuilder') {
                // Hide Quick Templates section on Bulk Search and Visual Builder tabs
                if (templatesSection) templatesSection.classList.add('hidden');
                // Hide Search History & Saved Searches section on Bulk Search and Visual Builder tabs
                if (searchHistorySection) searchHistorySection.classList.add('hidden');
                // Hide AI Suggestions section on Bulk Search and Visual Builder tabs
                if (aiSuggestionsSection) aiSuggestionsSection.classList.add('hidden');
            } else {
                // Show Quick Templates section on Basic Search tab
                if (templatesSection) templatesSection.classList.remove('hidden');
                // Show Search History & Saved Searches section on Basic Search tab
                if (searchHistorySection) searchHistorySection.classList.remove('hidden');
                // Show AI Suggestions section on Basic Search tab
                if (aiSuggestionsSection) aiSuggestionsSection.classList.remove('hidden');
            }
        }

        // Modal Functions (removed duplicates - using proper modal functions above)

        // Perform search function for keyboard shortcut
        function performSearch() {
            const generateBtn = document.getElementById('generateQuery');
            if (generateBtn) {
                generateBtn.click();
            }
        }

        // Keyboard Shortcuts
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey) {
                switch(e.key) {
                    case 'Enter':
                        e.preventDefault();
                        performSearch();
                        break;
                    case 'r':
                        e.preventDefault();
                        resetForm();
                        break;
                    case 's':
                        e.preventDefault();
                        document.getElementById('toggleSearchSettings').click();
                        break;
                    case 'b':
                        e.preventDefault();
                        switchTab('bulk');
                        break;
                    case 'q':
                        e.preventDefault();
                        switchTab('visualBuilder');
                        break;
                    case 't':
                        e.preventDefault();
                        // Only toggle templates if they're visible (not on bulk search tab)
                        const templatesSection = document.querySelector('.mb-6:has(#toggleTemplates)');
                        if (templatesSection && !templatesSection.classList.contains('hidden')) {
                            document.getElementById('toggleTemplates').click();
                        }
                        break;
                }
            }
        });

        function resetForm() {
            document.querySelectorAll('input, select, textarea').forEach(element => {
                if (element.type === 'checkbox' || element.type === 'radio') {
                    element.checked = false;
                } else {
                    element.value = '';
                }
            });

            // Reset templates toggle state
            const templatesContainer = document.getElementById('templatesContainer');
            const toggleButton = document.getElementById('toggleTemplates');
            const chevron = toggleButton.querySelector('i:last-child');

            if (templatesContainer && !templatesContainer.classList.contains('hidden')) {
                templatesContainer.classList.add('hidden');
                chevron.classList.remove('fa-chevron-up');
                chevron.classList.add('fa-chevron-down');
            }

            // Clear any success/error messages
            const messageEl = document.getElementById('globalMessages');
            if (messageEl) {
                messageEl.innerHTML = '';
            }
        }

        // Bulk Search Functionality
        function updateBulkCounts() {
            const names = document.getElementById('bulkNames').value.split('\n').filter(n => n.trim()).length;
            const titles = document.getElementById('bulkTitles').value.split('\n').filter(t => t.trim()).length;
            const locations = document.getElementById('bulkLocations').value.split('\n').filter(l => l.trim()).length;

            document.getElementById('namesCount').textContent = `${names} people`;
            document.getElementById('titlesCount').textContent = `${titles} interests`;
            document.getElementById('locationsCount').textContent = `${locations} locations`;

            // Update matrix calculations for targeted Facebook searches
            document.getElementById('namesTitlesMatrix').textContent = names * titles;
            document.getElementById('namesLocationsMatrix').textContent = names * locations;
            document.getElementById('titlesLocationsMatrix').textContent = titles * locations;
            document.getElementById('fullMatrix').textContent = names * titles * locations;
        }

        // File Upload Handler
        function handleFileUpload(event, targetTextareaId, type) {
            const file = event.target.files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onload = function(e) {
                const content = e.target.result;
                let lines = [];

                if (file.name.endsWith('.csv')) {
                    // Handle CSV files
                    lines = content.split('\n').map(line => line.split(',')[0].trim()).filter(line => line);
                } else {
                    // Handle TXT files
                    lines = content.split('\n').map(line => line.trim()).filter(line => line);
                }

                const textarea = document.getElementById(targetTextareaId);
                const existingContent = textarea.value.trim();
                const newContent = existingContent ? existingContent + '\n' + lines.join('\n') : lines.join('\n');

                textarea.value = newContent;
                updateBulkCounts();

                showAISuggestionMessage(`Successfully uploaded ${lines.length} ${type} from ${file.name}!`);
            };

            reader.readAsText(file);
        }

        // Generate Facebook Bulk Queries
        function generateFacebookBulkQueries() {
            // Check license tier for bulk search (requires Professional or Enterprise)
            if (!checkLicenseTier('bulk_search', 'bulk search functionality')) {
                return;
            }

            const names = document.getElementById('bulkNames').value.split('\n').filter(n => n.trim());
            const interests = document.getElementById('bulkTitles').value.split('\n').filter(t => t.trim());
            const locations = document.getElementById('bulkLocations').value.split('\n').filter(l => l.trim());



            if (names.length === 0 && interests.length === 0 && locations.length === 0) {
                showAISuggestionMessage('Please add at least one name, interest, or location to generate queries.', 'error');
                return;
            }

            const queries = [];
            let queryCount = 0;

            // Helper function to create Facebook search URL
            function createFacebookSearchURL(searchTerms) {
                const query = searchTerms.join(' ');
                const encodedQuery = encodeURIComponent(query);

                return `${window.APP_CONFIG.facebookSearchBaseUrl}people/?q=${encodedQuery}`;
            }

            // Generate Facebook-compatible targeted search queries
            // Facebook works better with specific, targeted searches rather than combining everything

            if (names.length > 0 && interests.length > 0 && locations.length > 0) {
                // Generate targeted combinations: Name + Interest + Location
                names.forEach(name => {
                    interests.forEach(interest => {
                        locations.forEach(location => {
                            // Create natural language query: "John Smith photography New York"
                            queries.push(createFacebookSearchURL([name, interest, location]));
                            queryCount++;
                        });
                    });
                });
            } else if (names.length > 0 && interests.length > 0) {
                // Name + Interest combinations
                names.forEach(name => {
                    interests.forEach(interest => {
                        queries.push(createFacebookSearchURL([name, interest]));
                        queryCount++;
                    });
                });
            } else if (names.length > 0 && locations.length > 0) {
                // Name + Location combinations
                names.forEach(name => {
                    locations.forEach(location => {
                        queries.push(createFacebookSearchURL([name, location]));
                        queryCount++;
                    });
                });
            } else if (interests.length > 0 && locations.length > 0) {
                // Interest + Location combinations
                interests.forEach(interest => {
                    locations.forEach(location => {
                        queries.push(createFacebookSearchURL([interest, location]));
                        queryCount++;
                    });
                });
            } else {
                // Single category searches
                names.forEach(name => {
                    queries.push(createFacebookSearchURL([name]));
                    queryCount++;
                });
                interests.forEach(interest => {
                    queries.push(createFacebookSearchURL([interest]));
                    queryCount++;
                });
                locations.forEach(location => {
                    queries.push(createFacebookSearchURL([location]));
                    queryCount++;
                });
            }

            // Display results
            displayBulkQueryResults(queries, queryCount);
        }

        // Load Saved Configurations
        function loadSavedConfigurations() {
            const configsList = document.getElementById('savedConfigurationsList');
            if (!configsList) return;

            if (window.savedConfigurations.length === 0) {
                configsList.innerHTML = '<div class="text-sm text-gray-500 dark:text-gray-400 text-center py-4">No saved configurations</div>';
                return;
            }

            configsList.innerHTML = window.savedConfigurations.map(config => `
                <div class="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-700 rounded-md">
                    <div class="flex-1">
                        <div class="text-sm font-medium text-gray-900 dark:text-white">${config.name}</div>
                        <div class="text-xs text-gray-500 dark:text-gray-400">${config.names.length} names • ${config.interests.length} interests • ${config.locations.length} locations</div>
                        <div class="text-xs text-gray-400 dark:text-gray-500">Saved: ${config.saved}</div>
                    </div>
                    <div class="flex space-x-1">
                        <button type="button" onclick="loadConfiguration(${config.id})" class="text-xs text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 p-1" title="Load">
                            <i class="fas fa-download"></i>
                        </button>
                        <button type="button" onclick="duplicateConfiguration(${config.id})" class="text-xs text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-300 p-1" title="Duplicate">
                            <i class="fas fa-copy"></i>
                        </button>
                        <button type="button" onclick="deleteConfiguration(${config.id})" class="text-xs text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 p-1" title="Delete">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            `).join('');
        }

        // Load Configuration
        function loadConfiguration(configId) {
            const config = window.savedConfigurations.find(c => c.id === configId);
            if (!config) return;

            document.getElementById('bulkNames').value = config.names.join('\n');
            document.getElementById('bulkTitles').value = config.interests.join('\n');
            document.getElementById('bulkLocations').value = config.locations.join('\n');

            updateBulkCounts();
            showAISuggestionMessage(`Configuration "${config.name}" loaded successfully!`);
        }

        // Duplicate Configuration
        function duplicateConfiguration(configId) {
            const config = window.savedConfigurations.find(c => c.id === configId);
            if (!config) return;

            const newConfig = {
                ...config,
                id: Date.now(),
                name: config.name + ' (Copy)',
                saved: new Date().toLocaleDateString()
            };

            window.savedConfigurations.push(newConfig);
            localStorage.setItem('facebookBulkConfigurations', JSON.stringify(window.savedConfigurations));
            loadSavedConfigurations();
            showAISuggestionMessage(`Configuration duplicated as "${newConfig.name}"!`);
        }

        // Delete Configuration
        function deleteConfiguration(configId) {
            if (!confirm('Are you sure you want to delete this configuration?')) return;

            window.savedConfigurations = window.savedConfigurations.filter(c => c.id !== configId);
            localStorage.setItem('facebookBulkConfigurations', JSON.stringify(window.savedConfigurations));
            loadSavedConfigurations();
            showAISuggestionMessage('Configuration deleted successfully!');
        }

        // Display Bulk Query Results
        function displayBulkQueryResults(queries, count) {
            // Helper function to extract search terms from URL for display
            function getDisplayText(url) {
                try {
                    const urlObj = new URL(url);
                    const searchParams = urlObj.searchParams.get('q');
                    if (searchParams) {
                        return decodeURIComponent(searchParams);
                    }
                    return url;
                } catch (e) {
                    return url;
                }
            }

            const resultsHtml = `
                <div class="bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900 dark:to-blue-900 border-2 border-green-200 dark:border-green-700 rounded-xl p-6 mt-6 shadow-lg">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-blue-600 rounded-full flex items-center justify-center mr-4">
                            <i class="fas fa-check text-white text-xl"></i>
                        </div>
                        <div>
                            <h4 class="text-xl font-bold text-green-800 dark:text-green-200">
                                Facebook Bulk Queries Generated!
                            </h4>
                            <p class="text-sm text-green-600 dark:text-green-300">
                                Successfully created ${count} search queries
                            </p>
                        </div>
                    </div>

                    <div class="bg-white dark:bg-gray-800 rounded-lg p-4 max-h-96 overflow-y-auto border border-gray-200 dark:border-gray-600">
                        <div class="space-y-2">
                            ${queries.map((query, index) => {
                                const displayText = getDisplayText(query);
                                const escapedUrl = query.replace(/'/g, "\\'");
                                return `
                                <div class="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-700 rounded-lg">
                                    <span class="text-sm text-gray-700 dark:text-gray-300">${index + 1}. ${displayText}</span>
                                    <div class="flex space-x-1">
                                        <button onclick="copyToClipboard('${escapedUrl}', this)" class="text-xs text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 px-2 py-1 rounded hover:bg-blue-50 dark:hover:bg-blue-900">
                                            <i class="fas fa-copy mr-1"></i>Copy
                                        </button>
                                        <button onclick="openFacebookSearch('${escapedUrl}', this)" class="text-xs text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-300 px-2 py-1 rounded hover:bg-green-50 dark:hover:bg-green-900">
                                            <i class="fas fa-search mr-1"></i>Search
                                        </button>
                                    </div>
                                </div>
                                `;
                            }).join('')}
                        </div>
                    </div>

                    <div class="flex gap-3 mt-4">
                        <button onclick="downloadQueries(${JSON.stringify(queries).replace(/"/g, '&quot;')})" class="flex-1 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white px-4 py-2 rounded-lg font-medium shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-300">
                            <i class="fas fa-download mr-2"></i>Download All Queries
                        </button>
                        <button onclick="copyAllQueries(${JSON.stringify(queries).replace(/"/g, '&quot;')})" class="bg-gradient-to-r from-green-500 to-teal-600 hover:from-green-600 hover:to-teal-700 text-white px-4 py-2 rounded-lg font-medium shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 transition-all duration-300">
                            <i class="fas fa-copy mr-2"></i>Copy All
                        </button>
                    </div>
                </div>
            `;

            // Insert results after the bulk search actions
            const actionsDiv = document.querySelector('#generateBulkQueries').parentElement;
            const existingResults = document.getElementById('bulkQueryResults');
            if (existingResults) {
                existingResults.remove();
            }

            const resultsDiv = document.createElement('div');
            resultsDiv.id = 'bulkQueryResults';
            resultsDiv.innerHTML = resultsHtml;
            actionsDiv.parentElement.appendChild(resultsDiv);

            showAISuggestionMessage(`Generated ${count} Facebook search queries successfully!`);
        }

        // Visual Query Builder Functionality
        let queryBlocks = [];

        function addQueryBlock(type, value) {
            const block = {
                id: Date.now(),
                type: type,
                value: value || ''
            };
            queryBlocks.push(block);
            renderQueryBuilder();
            updateVisualQuery();
        }

        function removeQueryBlock(id) {
            queryBlocks = queryBlocks.filter(block => block.id !== id);
            renderQueryBuilder();
            updateVisualQuery();
        }

        function renderQueryBuilder() {
            const container = document.getElementById('queryBuilder');

            if (queryBlocks.length === 0) {
                container.innerHTML = `
                    <div class="query-builder-empty">
                        <i class="fas fa-mouse-pointer text-4xl mb-2"></i>
                        <div>Click "Add" buttons above to build your query visually</div>
                        <div class="text-xs mt-1">Drag blocks to reorder • Click × to remove</div>
                    </div>
                `;
                return;
            }

            container.innerHTML = queryBlocks.map(block => `
                <div class="query-block" data-id="${block.id}">
                    <span>${block.type}: ${block.value || 'Enter value'}</span>
                    <button class="remove-btn" onclick="removeQueryBlock(${block.id})">×</button>
                </div>
            `).join('');
        }

        function updateVisualQuery() {
            const output = document.getElementById('visualQueryOutput');
            if (queryBlocks.length === 0) {
                output.textContent = 'Query will appear here...';
                return;
            }

            const query = queryBlocks.map(block => `${block.type}:"${block.value || 'VALUE'}"`).join(' AND ');
            output.textContent = query;
        }

        // Initialize tooltips and other interactive elements
        document.addEventListener('DOMContentLoaded', function() {
            // Check license status on page load
            updateLicenseStatus();

            // Initialize sample search history if none exists
            initializeSampleSearchHistory();

            // Header Button Functionality
            document.getElementById('manageLicense').addEventListener('click', function() {
                showLicenseModal('manage');
            });

            document.getElementById('toggleAnalytics').addEventListener('click', function() {
                showAnalyticsModal();
            });

            document.getElementById('toggleFavorites').addEventListener('click', function() {
                showFavoritesModal();
            });

            // Quick Templates Toggle
            document.getElementById('toggleTemplates').addEventListener('click', function() {
                const container = document.getElementById('templatesContainer');
                if (container) {
                    container.classList.toggle('hidden');
                }
            });

            // Search History Toggle
            document.getElementById('toggleSearchHistory').addEventListener('click', function() {
                const container = document.getElementById('searchHistoryContainer');
                if (container) {
                    container.classList.toggle('hidden');
                }
            });

            // History Tab Functionality
            document.querySelectorAll('.history-tab').forEach(tab => {
                tab.addEventListener('click', function() {
                    // Remove active class from all tabs
                    document.querySelectorAll('.history-tab').forEach(t => {
                        t.classList.remove('active', 'border-facebook-blue', 'text-facebook-blue');
                        t.classList.add('border-transparent', 'text-gray-500');
                    });

                    // Add active class to clicked tab
                    this.classList.add('active', 'border-facebook-blue', 'text-facebook-blue');
                    this.classList.remove('border-transparent', 'text-gray-500');

                    // Hide all content divs
                    document.getElementById('recentSearchesList').classList.add('hidden');
                    document.getElementById('savedSearchesList').classList.add('hidden');
                    document.getElementById('favoritesList').classList.add('hidden');

                    // Show appropriate content
                    const tabText = this.textContent.trim();
                    if (tabText === 'Recent Searches') {
                        document.getElementById('recentSearchesList').classList.remove('hidden');
                    } else if (tabText === 'Saved Searches') {
                        document.getElementById('savedSearchesList').classList.remove('hidden');
                    } else if (tabText === 'Favorites') {
                        document.getElementById('favoritesList').classList.remove('hidden');
                        updateFavoritesDisplay();
                    }
                });
            });

            // Search Settings Toggle
            document.getElementById('toggleSearchSettings').addEventListener('click', function() {
                const content = document.getElementById('searchSettingsContent');
                const chevron = document.getElementById('settingsChevron');

                content.classList.toggle('hidden');
                chevron.classList.toggle('rotate-180');
            });

            // Search Engine Selection
            document.querySelectorAll('.search-engine-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    document.querySelectorAll('.search-engine-btn').forEach(b => b.classList.remove('active'));
                    this.classList.add('active');
                });
            });

            // Exclude Terms Functionality
            // excludeTerms is now global - defined above
            const maxTerms = 10;

            // Add exclude term functionality
            function addExcludeTerm(term) {
                if (term && !excludeTerms.includes(term) && excludeTerms.length < maxTerms) {
                    excludeTerms.push(term);
                    updateExcludeDisplay();
                    updateExcludeCounter();
                }
            }

            function removeExcludeTerm(term) {
                excludeTerms = excludeTerms.filter(t => t !== term);
                updateExcludeDisplay();
                updateExcludeCounter();
            }

            // Make removeExcludeTerm globally accessible for onclick handlers
            window.removeExcludeTerm = removeExcludeTerm;

            function updateExcludeDisplay() {
                const container = document.getElementById('excludeTagsSettings');
                if (excludeTerms.length === 0) {
                    container.innerHTML = '<div class="text-xs text-gray-500 dark:text-gray-400">No exclude terms added</div>';
                } else {
                    container.innerHTML = excludeTerms.map(term =>
                        `<span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200 mr-1 mb-1">
                            ${term}
                            <button type="button" class="ml-1 text-red-600 hover:text-red-800" onclick="removeExcludeTerm('${term}')">
                                <i class="fas fa-times"></i>
                            </button>
                        </span>`
                    ).join('');
                }
            }

            function updateExcludeCounter() {
                const counter = document.getElementById('excludeCounterSettings');
                if (counter) {
                    counter.textContent = `${excludeTerms.length}/${maxTerms} terms`;
                }

                // Show/hide clear all button
                const clearBtn = document.getElementById('clearAllExcludesSettings');
                if (clearBtn) {
                    clearBtn.classList.toggle('hidden', excludeTerms.length === 0);
                }
            }

            // Make exclude functions globally accessible
            window.updateExcludeDisplay = updateExcludeDisplay;
            window.updateExcludeCounter = updateExcludeCounter;

            // Remove duplicate function - using the one above

            // Exclude term input handler
            document.getElementById('negativeSettings').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    const term = this.value.trim();
                    if (term) {
                        addExcludeTerm(term);
                        this.value = '';
                    }
                }
            });

            // Add exclude term button
            document.getElementById('addExcludeTermSettings').addEventListener('click', function() {
                const input = document.getElementById('negativeSettings');
                const term = input.value.trim();
                if (term) {
                    addExcludeTerm(term);
                    input.value = '';
                }
            });

            // Clear all excludes
            document.getElementById('clearAllExcludesSettings').addEventListener('click', function() {
                excludeTerms = [];
                updateExcludeDisplay();
                updateExcludeCounter();
            });

            // Exclude suggestions
            document.querySelectorAll('.exclude-suggestion').forEach(btn => {
                btn.addEventListener('click', function() {
                    addExcludeTerm(this.dataset.term);
                });
            });

            // Make removeExcludeTerm globally accessible
            window.removeExcludeTerm = removeExcludeTerm;

            // Initialize exclude counter
            updateExcludeCounter();

            // Templates functionality
            function loadTemplates() {
                const container = document.getElementById('templatesContainer');
                if (!container) return;

                container.innerHTML = facebookTemplates.map(template => `
                    <div class="template-card bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700 hover:border-facebook-blue cursor-pointer transition-all duration-200"
                         onclick="applyTemplate('${template.name}')">
                        <h4 class="font-semibold text-gray-800 dark:text-gray-200 mb-2">${template.name}</h4>
                        <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">${template.description}</p>
                        <div class="flex flex-wrap gap-1">
                            <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded">${template.data.field || 'General'}</span>
                            <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded">${template.data.jobTitle || 'Professional'}</span>
                        </div>
                    </div>
                `).join('');
            }

            function applyTemplate(templateName) {
                const template = facebookTemplates.find(t => t.name === templateName);
                if (template && template.data) {
                    // Set search type
                    const searchTypeSelect = document.getElementById('searchType');
                    if (searchTypeSelect && template.data.searchType) {
                        searchTypeSelect.value = template.data.searchType;
                    }

                    // Set field
                    const fieldInput = document.getElementById('field');
                    if (fieldInput && template.data.field) {
                        fieldInput.value = template.data.field;
                    }

                    // Set job title
                    const jobTitleInput = document.getElementById('jobTitle');
                    if (jobTitleInput && template.data.jobTitle) {
                        jobTitleInput.value = template.data.jobTitle;
                    }

                    // Set location
                    const locationInput = document.getElementById('location');
                    if (locationInput && template.data.location) {
                        locationInput.value = template.data.location;
                    }

                    // Show success message
                    console.log(`Template "${templateName}" applied successfully!`);
                    console.log('Applied template:', templateName, template.data);

                    // Hide templates container after applying template
                    const container = document.getElementById('templatesContainer');
                    const toggleButton = document.getElementById('toggleTemplates');
                    const chevron = toggleButton.querySelector('i:last-child');

                    container.classList.add('hidden');
                    chevron.classList.remove('fa-chevron-up');
                    chevron.classList.add('fa-chevron-down');
                }
            }

            // Load templates on page load
            loadTemplates();

            // Make applyTemplate globally accessible
            window.applyTemplate = applyTemplate;

            // Remove duplicate function - using updateExcludeDisplay() instead

            // Exclude terms functionality would go here
            // Currently disabled to prevent JavaScript errors

            // Bulk Search Event Listeners
            document.getElementById('bulkNames').addEventListener('input', updateBulkCounts);
            document.getElementById('bulkTitles').addEventListener('input', updateBulkCounts);
            document.getElementById('bulkLocations').addEventListener('input', updateBulkCounts);

            // Bulk Search Toggle Buttons
            document.getElementById('toggleNames').addEventListener('click', function() {
                const content = document.getElementById('namesContent');
                const icon = this.querySelector('i');
                const text = this.querySelector('span');

                content.classList.toggle('hidden');
                icon.classList.toggle('fa-chevron-up');
                icon.classList.toggle('fa-chevron-down');
                text.textContent = content.classList.contains('hidden') ? 'Expand' : 'Collapse';
            });

            document.getElementById('toggleTitles').addEventListener('click', function() {
                const content = document.getElementById('titlesContent');
                const icon = this.querySelector('i');
                const text = this.querySelector('span');

                content.classList.toggle('hidden');
                icon.classList.toggle('fa-chevron-up');
                icon.classList.toggle('fa-chevron-down');
                text.textContent = content.classList.contains('hidden') ? 'Expand' : 'Collapse';
            });

            document.getElementById('toggleLocations').addEventListener('click', function() {
                const content = document.getElementById('locationsContent');
                const icon = this.querySelector('i');
                const text = this.querySelector('span');

                content.classList.toggle('hidden');
                icon.classList.toggle('fa-chevron-up');
                icon.classList.toggle('fa-chevron-down');
                text.textContent = content.classList.contains('hidden') ? 'Expand' : 'Collapse';
            });

            // Bulk Search Settings Toggle
            if (document.getElementById('toggleBulkSettings')) {
                document.getElementById('toggleBulkSettings').addEventListener('click', function() {
                    const content = document.getElementById('bulkSettingsContent');
                    const icon = document.getElementById('bulkSettingsIcon');
                    const text = document.getElementById('bulkSettingsText');

                    if (content.style.display === 'none') {
                        content.style.display = 'block';
                        icon.classList.remove('fa-chevron-down');
                        icon.classList.add('fa-chevron-up');
                        text.textContent = 'Collapse';
                    } else {
                        content.style.display = 'none';
                        icon.classList.remove('fa-chevron-up');
                        icon.classList.add('fa-chevron-down');
                        text.textContent = 'Expand';
                    }
                });
            }

            // Initialize settings as collapsed on page load
            if (document.getElementById('bulkSettingsContent')) {
                document.getElementById('bulkSettingsContent').style.display = 'none';
                document.getElementById('bulkSettingsIcon').classList.remove('fa-chevron-up');
                document.getElementById('bulkSettingsIcon').classList.add('fa-chevron-down');
                document.getElementById('bulkSettingsText').textContent = 'Expand';
            }

            // Configuration Management - Global scope
            window.savedConfigurations = JSON.parse(localStorage.getItem('facebookBulkConfigurations') || '[]');

            // Save Configuration
            if (document.getElementById('saveConfiguration')) {
                document.getElementById('saveConfiguration').addEventListener('click', function() {
                    const configName = document.getElementById('configurationName').value.trim();
                    if (!configName) {
                        showAISuggestionMessage('Please enter a configuration name.', 'error');
                        return;
                    }

                    const names = document.getElementById('bulkNames').value.split('\n').filter(n => n.trim());
                    const interests = document.getElementById('bulkTitles').value.split('\n').filter(t => t.trim());
                    const locations = document.getElementById('bulkLocations').value.split('\n').filter(l => l.trim());

                    const configuration = {
                        id: Date.now(),
                        name: configName,
                        names: names,
                        interests: interests,
                        locations: locations,
                        saved: new Date().toLocaleDateString()
                    };

                    window.savedConfigurations.push(configuration);
                    localStorage.setItem('facebookBulkConfigurations', JSON.stringify(window.savedConfigurations));

                    showAISuggestionMessage(`Configuration "${configName}" saved successfully!`);
                    document.getElementById('configurationName').value = '';
                    loadSavedConfigurations();
                });
            }

            // Export Configurations
            if (document.getElementById('exportConfigurations')) {
                document.getElementById('exportConfigurations').addEventListener('click', function() {
                    if (window.savedConfigurations.length === 0) {
                        showAISuggestionMessage('No configurations to export.', 'error');
                        return;
                    }

                    const dataStr = JSON.stringify(window.savedConfigurations, null, 2);
                    const dataBlob = new Blob([dataStr], {type: 'application/json'});
                    const url = URL.createObjectURL(dataBlob);
                    const link = document.createElement('a');
                    link.href = url;
                    link.download = 'facebook-bulk-configurations.json';
                    link.click();
                    URL.revokeObjectURL(url);

                    showAISuggestionMessage('Configurations exported successfully!');
                });
            }

            // Import Configurations
            if (document.getElementById('importConfigurations')) {
                document.getElementById('importConfigurations').addEventListener('click', function() {
                    const input = document.createElement('input');
                    input.type = 'file';
                    input.accept = '.json';
                    input.onchange = function(e) {
                        const file = e.target.files[0];
                        if (!file) return;

                        const reader = new FileReader();
                        reader.onload = function(e) {
                            try {
                                const importedConfigs = JSON.parse(e.target.result);
                                if (Array.isArray(importedConfigs)) {
                                    window.savedConfigurations = [...window.savedConfigurations, ...importedConfigs];
                                    localStorage.setItem('facebookBulkConfigurations', JSON.stringify(window.savedConfigurations));
                                    loadSavedConfigurations();
                                    showAISuggestionMessage(`Imported ${importedConfigs.length} configurations successfully!`);
                                } else {
                                    showAISuggestionMessage('Invalid configuration file format.', 'error');
                                }
                            } catch (error) {
                                showAISuggestionMessage('Error reading configuration file.', 'error');
                            }
                        };
                        reader.readAsText(file);
                    };
                    input.click();
                });
            }

            // Clear All Configurations
            if (document.getElementById('clearAllConfigurations')) {
                document.getElementById('clearAllConfigurations').addEventListener('click', function() {
                    if (!confirm('Are you sure you want to delete all saved configurations? This cannot be undone.')) return;

                    window.savedConfigurations = [];
                    localStorage.setItem('facebookBulkConfigurations', JSON.stringify(window.savedConfigurations));
                    loadSavedConfigurations();
                    showAISuggestionMessage('All configurations cleared successfully!');
                });
            }

            // Load Sample Configuration
            if (document.getElementById('loadSampleConfiguration')) {
                document.getElementById('loadSampleConfiguration').addEventListener('click', function() {
                    document.getElementById('bulkNames').value = 'Jennifer\nMike\nDavid';
                    document.getElementById('bulkTitles').value = 'Travel\nMusic\nchurch';
                    document.getElementById('bulkLocations').value = 'New York City\nDenver';

                    updateBulkCounts();
                    showAISuggestionMessage('Sample configuration loaded successfully!');
                });
            }

            // Reset Configuration
            if (document.getElementById('resetConfiguration')) {
                document.getElementById('resetConfiguration').addEventListener('click', function() {
                    if (!confirm('Are you sure you want to clear all current data?')) return;

                    document.getElementById('bulkNames').value = '';
                    document.getElementById('bulkTitles').value = '';
                    document.getElementById('bulkLocations').value = '';

                    updateBulkCounts();
                    showAISuggestionMessage('All fields cleared successfully!');
                });
            }

            // Load saved configurations on page load
            loadSavedConfigurations();

            // Clear buttons
            document.getElementById('clearNames').addEventListener('click', function() {
                document.getElementById('bulkNames').value = '';
                updateBulkCounts();
            });

            document.getElementById('clearTitles').addEventListener('click', function() {
                document.getElementById('bulkTitles').value = '';
                updateBulkCounts();
            });

            document.getElementById('clearLocations').addEventListener('click', function() {
                document.getElementById('bulkLocations').value = '';
                updateBulkCounts();
            });

            // File Upload Functionality
            document.getElementById('uploadNamesBtn').addEventListener('click', function() {
                document.getElementById('namesFileUpload').click();
            });

            document.getElementById('uploadTitlesBtn').addEventListener('click', function() {
                document.getElementById('titlesFileUpload').click();
            });

            document.getElementById('uploadLocationsBtn').addEventListener('click', function() {
                document.getElementById('locationsFileUpload').click();
            });

            // File Upload Handlers
            document.getElementById('namesFileUpload').addEventListener('change', function(e) {
                handleFileUpload(e, 'bulkNames', 'people');
            });

            document.getElementById('titlesFileUpload').addEventListener('change', function(e) {
                handleFileUpload(e, 'bulkTitles', 'interests');
            });

            document.getElementById('locationsFileUpload').addEventListener('change', function(e) {
                handleFileUpload(e, 'bulkLocations', 'locations');
            });

            // Generate Bulk Queries Button
            document.getElementById('generateBulkQueries').addEventListener('click', function() {
                generateFacebookBulkQueries();
            });

            // Clear All Bulk Button
            document.getElementById('clearAllBulk').addEventListener('click', function() {
                document.getElementById('bulkNames').value = '';
                document.getElementById('bulkTitles').value = '';
                document.getElementById('bulkLocations').value = '';
                updateBulkCounts();
                showAISuggestionMessage('All bulk search data cleared!');
            });

            // Expand All/Collapse All Buttons
            document.getElementById('expandAllSections').addEventListener('click', function() {
                const sections = ['namesContent', 'titlesContent', 'locationsContent'];
                sections.forEach(sectionId => {
                    const content = document.getElementById(sectionId);
                    const toggleBtn = content.previousElementSibling.querySelector('button[id^="toggle"]');
                    const icon = toggleBtn.querySelector('i');
                    const text = toggleBtn.querySelector('span');

                    if (content.classList.contains('hidden')) {
                        content.classList.remove('hidden');
                        icon.classList.remove('fa-chevron-down');
                        icon.classList.add('fa-chevron-up');
                        text.textContent = 'Collapse';
                    }
                });
                showAISuggestionMessage('All sections expanded!');
            });

            document.getElementById('collapseAllSections').addEventListener('click', function() {
                const sections = ['namesContent', 'titlesContent', 'locationsContent'];
                sections.forEach(sectionId => {
                    const content = document.getElementById(sectionId);
                    const toggleBtn = content.previousElementSibling.querySelector('button[id^="toggle"]');
                    const icon = toggleBtn.querySelector('i');
                    const text = toggleBtn.querySelector('span');

                    if (!content.classList.contains('hidden')) {
                        content.classList.add('hidden');
                        icon.classList.remove('fa-chevron-up');
                        icon.classList.add('fa-chevron-down');
                        text.textContent = 'Expand';
                    }
                });
                showAISuggestionMessage('All sections collapsed!');
            });

            // Visual Query Builder Event Listeners
            document.querySelectorAll('.query-add-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const field = this.dataset.field;
                    const value = prompt(`Enter ${field}:`);
                    if (value) {
                        addQueryBlock(field, value);
                    }
                });
            });

            document.getElementById('clearQueryBuilder').addEventListener('click', function() {
                queryBlocks = [];
                renderQueryBuilder();
                updateVisualQuery();
            });

            document.getElementById('generateVisualQuery').addEventListener('click', function() {
                if (queryBlocks.length === 0) {
                    alert('Please add some query blocks first!');
                    return;
                }

                const query = queryBlocks.map(block => `${block.type}:"${block.value}"`).join(' AND ');
                document.getElementById('visualQueryDisplay').value = query;
                document.getElementById('visualQueryResults').classList.remove('hidden');
            });

            document.getElementById('clearVisualBuilder').addEventListener('click', function() {
                queryBlocks = [];
                renderQueryBuilder();
                updateVisualQuery();
                document.getElementById('visualQueryResults').classList.add('hidden');
            });

            document.getElementById('copyVisualQuery').addEventListener('click', function() {
                const query = document.getElementById('visualQueryDisplay').value;
                navigator.clipboard.writeText(query).then(() => {
                    alert('Query copied to clipboard!');
                });
            });

            // Initialize bulk counts
            updateBulkCounts();

            // LinkedIn-style name validation
            const nameInput = document.getElementById('name');
            if (nameInput) {
                nameInput.addEventListener('input', function() {
                    const value = this.value.trim();
                    const nameValidation = this.parentElement.querySelector('.name-validation');

                    // Check if it looks like a valid name (at least 2 words, each with 2+ characters)
                    const namePattern = /^[a-zA-Z]{2,}\s+[a-zA-Z]{2,}(\s+[a-zA-Z]{2,})*$/;
                    const isValid = namePattern.test(value);

                    if (isValid) {
                        this.classList.add('valid');
                        if (nameValidation) nameValidation.classList.remove('hidden');
                    } else {
                        this.classList.remove('valid');
                        if (nameValidation) nameValidation.classList.add('hidden');
                    }
                });
            }

            // Contact Enrichment Toggle
            const enrichmentToggle = document.getElementById('enrichmentToggle');
            if (enrichmentToggle) {
                enrichmentToggle.addEventListener('change', function() {
                    // Check license tier for enrichment
                    if (!hasFeature('basic_enrichment')) {
                        // Revert the toggle and show upgrade modal
                        this.checked = false;
                        showUpgradeRequiredModal('basic_enrichment', 'contact enrichment');
                        return;
                    }

                    const isEnabled = this.checked;
                    const statusText = this.parentElement.parentElement.querySelector('span');

                    if (statusText) {
                        statusText.textContent = isEnabled ? 'Enabled' : 'Disabled';
                    }

                    // Save enrichment preference
                    localStorage.setItem('contactEnrichmentEnabled', isEnabled);

                    // Show feedback message
                    showSuccess(`Contact enrichment ${isEnabled ? 'enabled' : 'disabled'}`);
                });

                // Load saved preference or set based on license
                const hasBasicEnrichment = hasFeature('basic_enrichment');
                if (hasBasicEnrichment) {
                    const savedPreference = localStorage.getItem('contactEnrichmentEnabled');
                    const isEnabled = savedPreference !== null ? savedPreference === 'true' : true;
                    enrichmentToggle.checked = isEnabled;
                    const statusText = enrichmentToggle.parentElement.parentElement.querySelector('span');
                    if (statusText) {
                        statusText.textContent = isEnabled ? 'Enabled' : 'Disabled';
                    }
                } else {
                    // No enrichment access
                    enrichmentToggle.checked = false;
                    enrichmentToggle.disabled = true;
                    const statusText = enrichmentToggle.parentElement.parentElement.querySelector('span');
                    if (statusText) {
                        statusText.textContent = 'License Required';
                        statusText.classList.add('text-red-500');
                    }
                }
            }

            // Contact Enrichment Settings
            const enrichmentSettings = document.getElementById('enrichmentSettings');
            if (enrichmentSettings) {
                enrichmentSettings.addEventListener('click', function(e) {
                    e.preventDefault();

                    // Check license tier for enrichment settings
                    if (!hasFeature('basic_enrichment')) {
                        showUpgradeRequiredModal('basic_enrichment', 'contact enrichment settings');
                        return;
                    }

                    // Show enrichment settings modal/panel
                    showEnrichmentSettings();
                });
            }

            // Initialize favorite searches
            initializeFavoriteSearches();

            // Data Quality Slider Event Listener
            const confidenceSlider = document.querySelector('#enrichmentSettingsContainer input[type="range"]');
            if (confidenceSlider) {
                confidenceSlider.addEventListener('input', function() {
                    // Find the percentage span that's specifically next to "Minimum Confidence Score"
                    const percentSpan = this.parentElement.querySelector('.text-sm.font-medium');
                    if (percentSpan) {
                        percentSpan.textContent = this.value + '%';
                    }
                });
            }

            // Load saved enrichment settings
            loadEnrichmentSettings();

            // Initialize license-based UI updates
            updateLicenseStatus();
            updateUIForLicenseTier();

            console.log('Facebook Graph Search initialized');
        });
    </script>
</body>
</html>