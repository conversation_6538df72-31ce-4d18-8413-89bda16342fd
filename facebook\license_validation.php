<?php
/**
 * Facebook License Validation System
 */

require_once 'config.php';

class FacebookLicenseValidator {
    private $apiUrl;
    private $apiKey;

    public function __construct() {
        $this->apiUrl = LICENSE_API_URL;
        $this->apiKey = 'facebook_app_key_2024';
    }

    /**
     * Validate license key with device fingerprint
     */
    public function validateLicense($licenseKey, $deviceFingerprint, $email = null) {
        $data = [
            'license_key' => $licenseKey,
            'device_fingerprint' => $deviceFingerprint,
            'email' => $email,
            'app_type' => 'facebook_search',
            'api_key' => $this->apiKey
        ];

        $response = $this->makeApiCall(LICENSE_VALIDATION_ENDPOINT, $data);

        if ($response === false) {
            return [
                'valid' => false,
                'reason' => 'Unable to connect to license server',
                'offline_mode' => true
            ];
        }

        return $response;
    }

    /**
     * Register device for license
     */
    public function registerDevice($licenseKey, $deviceFingerprint, $deviceInfo = []) {
        $data = [
            'license_key' => $licenseKey,
            'device_fingerprint' => $deviceFingerprint,
            'device_info' => $deviceInfo,
            'app_type' => 'facebook_search',
            'api_key' => $this->apiKey
        ];

        return $this->makeApiCall(DEVICE_REGISTRATION_ENDPOINT, $data);
    }

    /**
     * Make API call to license server
     */
    private function makeApiCall($endpoint, $data) {
        $url = $this->apiUrl . $endpoint;

        $options = [
            'http' => [
                'header' => "Content-type: application/x-www-form-urlencoded\r\n",
                'method' => 'POST',
                'content' => http_build_query($data),
                'timeout' => 10
            ]
        ];

        $context = stream_context_create($options);
        $result = @file_get_contents($url, false, $context);

        if ($result === false) {
            return false;
        }

        return json_decode($result, true);
    }

    /**
     * Generate device fingerprint
     */
    public static function generateDeviceFingerprint($userAgent, $additionalData = []) {
        $fingerprint = [
            'user_agent' => $userAgent,
            'ip_hash' => hash('sha256', $_SERVER['REMOTE_ADDR'] ?? ''),
            'timestamp' => time(),
            'additional' => $additionalData
        ];

        return hash('sha256', json_encode($fingerprint));
    }

    /**
     * Check if license allows feature
     */
    public function checkFeatureAccess($licenseData, $feature) {
        if (!isset($licenseData['features'])) {
            return false;
        }

        return $licenseData['features'][$feature] ?? false;
    }
}

// Usage example:
/*
$validator = new FacebookLicenseValidator();
$deviceFingerprint = FacebookLicenseValidator::generateDeviceFingerprint($_SERVER['HTTP_USER_AGENT']);
$result = $validator->validateLicense('FB-TRIAL-2024', $deviceFingerprint, '<EMAIL>');

if ($result['valid']) {
    echo "License is valid!";
} else {
    echo "License validation failed: " . $result['reason'];
}
*/
?>