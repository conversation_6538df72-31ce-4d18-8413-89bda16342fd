<?php
session_start();

// Include centralized configuration
require_once __DIR__ . '/../config.php';

// Check if user is logged in
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit();
}

header('Content-Type: application/json');

// Get database connection from centralized config
try {
    $pdo = getFacebookDatabase();
} catch (Exception $e) {
    logFacebookError('License API database connection failed', ['error' => $e->getMessage()]);
    echo json_encode(['success' => false, 'error' => 'Database connection failed: ' . $e->getMessage()]);
    exit();
}

// Get current licenses from database
function getLicenses($pdo, $search = '', $type = '', $status = '') {
    $sql = "SELECT * FROM licenses WHERE 1=1";
    $params = [];

    if ($search) {
        $sql .= " AND (license_key LIKE ? OR customer_name LIKE ? OR customer_email LIKE ?)";
        $searchParam = "%$search%";
        $params[] = $searchParam;
        $params[] = $searchParam;
        $params[] = $searchParam;
    }

    if ($type) {
        $sql .= " AND license_type = ?";
        $params[] = strtolower($type);
    }

    if ($status) {
        $sql .= " AND status = ?";
        $params[] = strtolower($status);
    }

    $sql .= " ORDER BY created_at DESC";

    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    return $stmt->fetchAll();
}

// Get license statistics
function getLicenseStats($pdo) {
    $stats = [];

    // Total licenses
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM licenses");
    $stats['total'] = $stmt->fetch()['total'];

    // Active licenses
    $stmt = $pdo->query("SELECT COUNT(*) as active FROM licenses WHERE status = 'active'");
    $stats['active'] = $stmt->fetch()['active'];

    // Expired licenses
    $stmt = $pdo->query("SELECT COUNT(*) as expired FROM licenses WHERE status = 'expired'");
    $stats['expired'] = $stmt->fetch()['expired'];

    // Revoked licenses
    $stmt = $pdo->query("SELECT COUNT(*) as revoked FROM licenses WHERE status = 'suspended'");
    $stats['revoked'] = $stmt->fetch()['revoked'];

    // License types
    $stmt = $pdo->query("SELECT license_type, COUNT(*) as count FROM licenses GROUP BY license_type");
    $types = $stmt->fetchAll();
    foreach ($types as $type) {
        $stats[strtolower($type['license_type'])] = $type['count'];
    }

    // Calculate revenue (mock calculation based on license types)
    $revenue = 0;
    $stmt = $pdo->query("SELECT license_type, COUNT(*) as count FROM licenses WHERE status = 'active' GROUP BY license_type");
    $activeTypes = $stmt->fetchAll();

    foreach ($activeTypes as $type) {
        switch (strtolower($type['license_type'])) {
            case 'basic': $revenue += $type['count'] * 19.9; break;
            case 'professional': $revenue += $type['count'] * 39.9; break;
            case 'enterprise': $revenue += $type['count'] * 99.9; break;
        }
    }
    $stats['monthly_revenue'] = $revenue;

    return $stats;
}

// Generate unique license key (using the same format as the existing database)
function generateLicenseKey($type) {
    $chars = '0123456789ABCDEF';
    $segments = [];

    // Generate 12 segments of 4 characters each, separated by dashes
    for ($i = 0; $i < 12; $i++) {
        $segment = '';
        for ($j = 0; $j < 4; $j++) {
            $segment .= $chars[rand(0, strlen($chars) - 1)];
        }
        $segments[] = $segment;
    }

    return implode('-', $segments);
}

// Handle different API actions
$action = $_GET['action'] ?? $_POST['action'] ?? '';

switch ($action) {
    case 'list':
        $search = $_GET['search'] ?? '';
        $type = $_GET['type'] ?? '';
        $status = $_GET['status'] ?? '';

        $licenses = getLicenses($pdo, $search, $type, $status);

        // Format licenses for frontend
        $formattedLicenses = [];
        foreach ($licenses as $license) {
            $formattedLicenses[] = [
                'id' => $license['id'],
                'license_key' => $license['license_key'],
                'user_email' => $license['customer_email'],
                'user_name' => $license['customer_name'],
                'type' => strtoupper($license['license_type']),
                'status' => ucfirst($license['status']),
                'created_date' => date('Y-m-d', strtotime($license['created_at'])),
                'expiry_date' => date('Y-m-d', strtotime($license['expires_at'])),
                'max_searches' => $license['max_devices'],
                'used_searches' => $license['total_searches'],
                'notes' => $license['notes'] ?? ''
            ];
        }

        echo json_encode(['success' => true, 'licenses' => $formattedLicenses]);
        break;
        
    case 'generate':
    case 'generate_license':
        // Handle both old and new action names
        $type = strtolower($_POST['license_type'] ?? $_POST['type'] ?? 'basic');
        $userEmail = $_POST['customer_email'] ?? $_POST['user_email'] ?? '';
        $userName = $_POST['customer_name'] ?? $_POST['user_name'] ?? '';
        $expiryMonths = (int)($_POST['duration_months'] ?? $_POST['expiry_months'] ?? 12);
        $maxDevices = (int)($_POST['max_devices'] ?? 3);
        $notes = $_POST['notes'] ?? '';

        if (!$userEmail || !$userName) {
            echo json_encode(['success' => false, 'error' => 'User email and name are required']);
            break;
        }

        // Validate license type
        $validTypes = ['basic', 'professional', 'enterprise'];
        if (!in_array($type, $validTypes)) {
            $type = 'basic';
        }

        try {
            $licenseKey = generateLicenseKey($type);
            $expiresAt = date('Y-m-d H:i:s', strtotime("+{$expiryMonths} months"));

            // Create license data JSON
            $licenseData = json_encode([
                'type' => $type,
                'email' => $userEmail,
                'generated_date' => date('Y-m-d H:i:s'),
                'version' => '2.0'
            ]);

            $sql = "INSERT INTO licenses (license_key, customer_name, customer_email, license_type, status, max_devices, duration_months, expires_at, notes, created_by, license_data) VALUES (?, ?, ?, ?, 'active', ?, ?, ?, ?, 1, ?)";

            $stmt = $pdo->prepare($sql);
            $result = $stmt->execute([
                $licenseKey,
                $userName,
                $userEmail,
                $type,
                $maxDevices,
                $expiryMonths,
                $expiresAt,
                $notes,
                $licenseData
            ]);

            if ($result) {
                $newLicense = [
                    'id' => $pdo->lastInsertId(),
                    'license_key' => $licenseKey,
                    'user_email' => $userEmail,
                    'user_name' => $userName,
                    'type' => strtoupper($type),
                    'status' => 'Active',
                    'created_date' => date('Y-m-d'),
                    'expiry_date' => date('Y-m-d', strtotime($expiresAt)),
                    'max_searches' => $maxDevices,
                    'used_searches' => 0,
                    'notes' => $notes
                ];

                echo json_encode(['success' => true, 'license' => $newLicense]);
            } else {
                echo json_encode(['success' => false, 'error' => 'Failed to save license to database']);
            }
        } catch (PDOException $e) {
            echo json_encode(['success' => false, 'error' => 'Database error: ' . $e->getMessage()]);
        }
        break;
        
    case 'extend':
        $licenseKey = $_POST['license_key'] ?? '';
        $months = (int)($_POST['months'] ?? 12);

        if (!$licenseKey) {
            echo json_encode(['success' => false, 'error' => 'License key is required']);
            break;
        }

        try {
            $sql = "UPDATE licenses SET expires_at = DATE_ADD(expires_at, INTERVAL ? MONTH), updated_at = CURRENT_TIMESTAMP WHERE license_key = ?";
            $stmt = $pdo->prepare($sql);
            $result = $stmt->execute([$months, $licenseKey]);

            if ($result && $stmt->rowCount() > 0) {
                echo json_encode(['success' => true, 'message' => "License extended by {$months} months"]);
            } else {
                echo json_encode(['success' => false, 'error' => 'License not found']);
            }
        } catch (PDOException $e) {
            echo json_encode(['success' => false, 'error' => 'Database error: ' . $e->getMessage()]);
        }
        break;

    case 'edit':
        $licenseKey = $_POST['license_key'] ?? '';
        $status = $_POST['status'] ?? '';
        $notes = $_POST['notes'] ?? '';

        if (!$licenseKey) {
            echo json_encode(['success' => false, 'error' => 'License key is required']);
            break;
        }

        try {
            $updates = [];
            $params = [];

            if ($status) {
                $updates[] = "status = ?";
                $params[] = strtolower($status);
            }

            if ($notes !== '') {
                $updates[] = "notes = ?";
                $params[] = $notes;
            }

            if (empty($updates)) {
                echo json_encode(['success' => false, 'error' => 'No updates provided']);
                break;
            }

            $updates[] = "updated_at = CURRENT_TIMESTAMP";
            $params[] = $licenseKey;

            $sql = "UPDATE licenses SET " . implode(', ', $updates) . " WHERE license_key = ?";
            $stmt = $pdo->prepare($sql);
            $result = $stmt->execute($params);

            if ($result && $stmt->rowCount() > 0) {
                echo json_encode(['success' => true, 'message' => 'License updated successfully']);
            } else {
                echo json_encode(['success' => false, 'error' => 'License not found']);
            }
        } catch (PDOException $e) {
            echo json_encode(['success' => false, 'error' => 'Database error: ' . $e->getMessage()]);
        }
        break;

    case 'revoke':
        $licenseKey = $_POST['license_key'] ?? '';

        if (!$licenseKey) {
            echo json_encode(['success' => false, 'error' => 'License key is required']);
            break;
        }

        try {
            $sql = "UPDATE licenses SET status = 'suspended', updated_at = CURRENT_TIMESTAMP WHERE license_key = ?";
            $stmt = $pdo->prepare($sql);
            $result = $stmt->execute([$licenseKey]);

            if ($result && $stmt->rowCount() > 0) {
                echo json_encode(['success' => true, 'message' => 'License revoked successfully']);
            } else {
                echo json_encode(['success' => false, 'error' => 'License not found']);
            }
        } catch (PDOException $e) {
            echo json_encode(['success' => false, 'error' => 'Database error: ' . $e->getMessage()]);
        }
        break;

    case 'delete':
        $licenseKey = $_POST['license_key'] ?? '';

        if (!$licenseKey) {
            echo json_encode(['success' => false, 'error' => 'License key is required']);
            break;
        }

        try {
            $sql = "DELETE FROM licenses WHERE license_key = ?";
            $stmt = $pdo->prepare($sql);
            $result = $stmt->execute([$licenseKey]);

            if ($result && $stmt->rowCount() > 0) {
                echo json_encode(['success' => true, 'message' => 'License deleted successfully']);
            } else {
                echo json_encode(['success' => false, 'error' => 'License not found']);
            }
        } catch (PDOException $e) {
            echo json_encode(['success' => false, 'error' => 'Database error: ' . $e->getMessage()]);
        }
        break;
        
    case 'stats':
        try {
            $stats = getLicenseStats($pdo);
            echo json_encode(['success' => true, 'stats' => $stats]);
        } catch (PDOException $e) {
            echo json_encode(['success' => false, 'error' => 'Database error: ' . $e->getMessage()]);
        }
        break;

    case 'export':
        try {
            $licenses = getLicenses($pdo);

            // Create CSV content
            $csv = "License Key,Customer Name,Customer Email,Type,Status,Created Date,Expiry Date,Max Devices,Total Searches,Notes\n";
            foreach ($licenses as $license) {
                $csv .= sprintf(
                    "%s,%s,%s,%s,%s,%s,%s,%s,%s,%s\n",
                    '"' . $license['license_key'] . '"',
                    '"' . $license['customer_name'] . '"',
                    '"' . $license['customer_email'] . '"',
                    '"' . ucfirst($license['license_type']) . '"',
                    '"' . ucfirst($license['status']) . '"',
                    '"' . date('Y-m-d', strtotime($license['created_at'])) . '"',
                    '"' . date('Y-m-d', strtotime($license['expires_at'])) . '"',
                    $license['max_devices'],
                    $license['total_searches'],
                    '"' . str_replace('"', '""', $license['notes'] ?? '') . '"'
                );
            }

            header('Content-Type: text/csv');
            header('Content-Disposition: attachment; filename="licenses_' . date('Y-m-d') . '.csv"');
            echo $csv;
            exit;
        } catch (PDOException $e) {
            echo json_encode(['success' => false, 'error' => 'Database error: ' . $e->getMessage()]);
        }
        break;
        
    default:
        echo json_encode(['success' => false, 'error' => 'Invalid action']);
        break;
}
?>
