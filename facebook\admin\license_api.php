<?php
/**
 * Facebook License API Management
 */

require_once '../config.php';

// Check admin authentication
session_start();
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: login.php');
    exit;
}

// Handle API requests
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';

    switch ($action) {
        case 'create_license':
            createLicense();
            break;
        case 'revoke_license':
            revokeLicense();
            break;
        case 'check_license':
            checkLicense();
            break;
        default:
            echo json_encode(['error' => 'Invalid action']);
            break;
    }
    exit;
}

function createLicense() {
    $licenseKey = 'FB-' . strtoupper(bin2hex(random_bytes(8)));
    $tier = $_POST['tier'] ?? 'trial';
    $deviceLimit = (int)($_POST['device_limit'] ?? 1);
    $duration = (int)($_POST['duration'] ?? 7); // days

    $license = [
        'key' => $licenseKey,
        'tier' => $tier,
        'deviceLimit' => $deviceLimit,
        'expirationDate' => date('Y-m-d H:i:s', strtotime("+{$duration} days")),
        'allowedDevices' => [],
        'created' => date('Y-m-d H:i:s'),
        'status' => 'active'
    ];

    // In a real application, save to database
    // For now, just return the license data
    echo json_encode([
        'success' => true,
        'license' => $license,
        'message' => 'License created successfully'
    ]);
}

function revokeLicense() {
    $licenseKey = $_POST['license_key'] ?? '';

    // In a real application, update database
    echo json_encode([
        'success' => true,
        'message' => "License {$licenseKey} has been revoked"
    ]);
}

function checkLicense() {
    $licenseKey = $_POST['license_key'] ?? '';

    // In a real application, query database
    echo json_encode([
        'success' => true,
        'license' => [
            'key' => $licenseKey,
            'status' => 'active',
            'tier' => 'trial',
            'devices_used' => 0,
            'device_limit' => 1
        ]
    ]);
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Facebook Admin - License API</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body class="bg-gray-100">
    <div class="min-h-screen">
        <!-- Header -->
        <header class="bg-facebook-blue text-white shadow-lg">
            <div class="container mx-auto px-4 py-4">
                <div class="flex justify-between items-center">
                    <h1 class="text-2xl font-bold">
                        <i class="fab fa-facebook mr-2"></i>Facebook License API
                    </h1>
                    <nav>
                        <a href="graphsearch.php" class="text-white hover:text-blue-200 mr-4">Graph Search</a>
                        <a href="logout.php" class="text-white hover:text-blue-200">Logout</a>
                    </nav>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="container mx-auto px-4 py-8">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Create License -->
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <h2 class="text-xl font-semibold mb-4">Create New License</h2>
                    <form id="createLicenseForm">
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium mb-2">License Tier</label>
                                <select name="tier" class="w-full p-2 border rounded">
                                    <option value="trial">Trial</option>
                                    <option value="basic">Basic</option>
                                    <option value="professional">Professional</option>
                                    <option value="enterprise">Enterprise</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium mb-2">Device Limit</label>
                                <input type="number" name="device_limit" value="1" min="1" max="10" class="w-full p-2 border rounded">
                            </div>
                            <div>
                                <label class="block text-sm font-medium mb-2">Duration (days)</label>
                                <input type="number" name="duration" value="7" min="1" max="365" class="w-full p-2 border rounded">
                            </div>
                            <button type="submit" class="w-full bg-blue-500 hover:bg-blue-600 text-white py-2 rounded">
                                <i class="fas fa-plus mr-2"></i>Create License
                            </button>
                        </div>
                    </form>
                </div>

                <!-- License Management -->
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <h2 class="text-xl font-semibold mb-4">License Management</h2>
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium mb-2">License Key</label>
                            <input type="text" id="licenseKeyInput" placeholder="Enter license key" class="w-full p-2 border rounded">
                        </div>
                        <div class="flex space-x-2">
                            <button id="checkLicenseBtn" class="flex-1 bg-green-500 hover:bg-green-600 text-white py-2 rounded">
                                <i class="fas fa-search mr-2"></i>Check
                            </button>
                            <button id="revokeLicenseBtn" class="flex-1 bg-red-500 hover:bg-red-600 text-white py-2 rounded">
                                <i class="fas fa-ban mr-2"></i>Revoke
                            </button>
                        </div>
                    </div>

                    <!-- Results -->
                    <div id="licenseResults" class="mt-6 hidden">
                        <h3 class="font-semibold mb-2">License Information</h3>
                        <div id="licenseInfo" class="bg-gray-50 p-4 rounded"></div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // Create License Form
        $('#createLicenseForm').on('submit', function(e) {
            e.preventDefault();

            $.post('', $(this).serialize() + '&action=create_license', function(response) {
                const data = JSON.parse(response);
                if (data.success) {
                    alert('License created: ' + data.license.key);
                } else {
                    alert('Error: ' + data.message);
                }
            });
        });

        // Check License
        $('#checkLicenseBtn').on('click', function() {
            const licenseKey = $('#licenseKeyInput').val();
            if (!licenseKey) {
                alert('Please enter a license key');
                return;
            }

            $.post('', {action: 'check_license', license_key: licenseKey}, function(response) {
                const data = JSON.parse(response);
                if (data.success) {
                    $('#licenseInfo').html(`
                        <p><strong>Key:</strong> ${data.license.key}</p>
                        <p><strong>Status:</strong> ${data.license.status}</p>
                        <p><strong>Tier:</strong> ${data.license.tier}</p>
                        <p><strong>Devices:</strong> ${data.license.devices_used}/${data.license.device_limit}</p>
                    `);
                    $('#licenseResults').removeClass('hidden');
                } else {
                    alert('Error: ' + data.message);
                }
            });
        });

        // Revoke License
        $('#revokeLicenseBtn').on('click', function() {
            const licenseKey = $('#licenseKeyInput').val();
            if (!licenseKey) {
                alert('Please enter a license key');
                return;
            }

            if (confirm('Are you sure you want to revoke this license?')) {
                $.post('', {action: 'revoke_license', license_key: licenseKey}, function(response) {
                    const data = JSON.parse(response);
                    alert(data.message);
                });
            }
        });
    </script>
</body>
</html>