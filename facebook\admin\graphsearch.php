<?php
/**
 * Facebook Admin Graph Search Interface
 */

require_once '../config.php';

// Check admin authentication
session_start();
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: login.php');
    exit;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Facebook Admin - Graph Search</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>
</head>
<body class="bg-gray-100">
    <div class="min-h-screen">
        <!-- Header -->
        <header class="bg-facebook-blue text-white shadow-lg">
            <div class="container mx-auto px-4 py-4">
                <div class="flex justify-between items-center">
                    <h1 class="text-2xl font-bold">
                        <i class="fab fa-facebook mr-2"></i>Facebook Admin Panel
                    </h1>
                    <nav>
                        <a href="dashboard.php" class="text-white hover:text-blue-200 mr-4">Dashboard</a>
                        <a href="license_api.php" class="text-white hover:text-blue-200 mr-4">License API</a>
                        <a href="logout.php" class="text-white hover:text-blue-200">Logout</a>
                    </nav>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="container mx-auto px-4 py-8">
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-xl font-semibold mb-4">Facebook Graph Search Management</h2>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <!-- Search Statistics -->
                    <div class="bg-blue-50 p-4 rounded-lg">
                        <h3 class="font-semibold text-blue-800 mb-2">Search Statistics</h3>
                        <p class="text-2xl font-bold text-blue-600">0</p>
                        <p class="text-sm text-blue-600">Total Searches Today</p>
                    </div>

                    <!-- Active Users -->
                    <div class="bg-green-50 p-4 rounded-lg">
                        <h3 class="font-semibold text-green-800 mb-2">Active Users</h3>
                        <p class="text-2xl font-bold text-green-600">0</p>
                        <p class="text-sm text-green-600">Users Online</p>
                    </div>

                    <!-- License Status -->
                    <div class="bg-purple-50 p-4 rounded-lg">
                        <h3 class="font-semibold text-purple-800 mb-2">License Status</h3>
                        <p class="text-2xl font-bold text-purple-600">0</p>
                        <p class="text-sm text-purple-600">Active Licenses</p>
                    </div>
                </div>

                <!-- Search Management Tools -->
                <div class="mt-8">
                    <h3 class="text-lg font-semibold mb-4">Search Management Tools</h3>
                    <div class="space-y-4">
                        <button class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded">
                            <i class="fas fa-search mr-2"></i>View Search Logs
                        </button>
                        <button class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded ml-2">
                            <i class="fas fa-users mr-2"></i>Manage Users
                        </button>
                        <button class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded ml-2">
                            <i class="fas fa-key mr-2"></i>License Management
                        </button>
                    </div>
                </div>
            </div>
        </main>
    </div>
</body>
</html>