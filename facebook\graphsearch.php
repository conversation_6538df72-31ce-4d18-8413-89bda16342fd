<?php
/**
 * Facebook Graph Search Application
 */

require_once 'config.php';

// Dynamic Configuration
$protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
$host = $_SERVER['HTTP_HOST'];
$scriptPath = dirname($_SERVER['SCRIPT_NAME']);
$baseUrl = $protocol . '://' . $host . $scriptPath;
$rootUrl = $protocol . '://' . $host;
?>
<!DOCTYPE html>
<html lang="en" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Facebook Graph Search</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>

    <script>
        // Dynamic Configuration for JavaScript
        window.APP_CONFIG = {
            baseUrl: '<?php echo $baseUrl; ?>',
            rootUrl: '<?php echo $rootUrl; ?>',
            adminUrl: '<?php echo $baseUrl; ?>/admin',
            supportUrl: 'https://wa.me/270618757667?text=Hello!%20I%20need%20assistance%20with%20Facebook%20Graph%20Search.'
        };

        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        facebook: {
                            blue: '#1877f2',
                            darkBlue: '#166fe5'
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-100 dark:bg-gray-900 text-gray-800 dark:text-gray-200">
    <div class="container mx-auto p-6 sm:p-8 max-w-5xl mt-6 sm:mt-12">
        <div class="bg-gradient-to-br from-facebook-blue to-facebook-darkBlue rounded-xl shadow-lg mb-8 overflow-hidden">
            <div class="px-6 py-8">
                <div class="flex items-center mb-4">
                    <div class="bg-white rounded-full p-3 mr-4">
                        <i class="fab fa-facebook text-2xl text-facebook-blue"></i>
                    </div>
                    <div>
                        <h1 class="text-3xl font-bold text-white mb-1">AI Facebook Graph Search</h1>
                        <p class="text-blue-100 text-sm">Advanced Facebook profile discovery and search optimization</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 class="text-xl font-semibold mb-4">Facebook Search Interface</h2>
            <p class="text-gray-600 dark:text-gray-400">Facebook Graph Search functionality will be implemented here.</p>
        </div>
    </div>
</body>
</html>